<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'Psr\\SimpleCache\\' => array($vendorDir . '/psr/simple-cache/src'),
    'PhpOffice\\PhpSpreadsheet\\' => array($vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet'),
    'Matrix\\' => array($vendorDir . '/markbaker/matrix/classes/src'),
    'Complex\\' => array($vendorDir . '/markbaker/complex/classes/src'),
    'Box\\Spout\\' => array($vendorDir . '/box/spout/src/Spout'),
);
