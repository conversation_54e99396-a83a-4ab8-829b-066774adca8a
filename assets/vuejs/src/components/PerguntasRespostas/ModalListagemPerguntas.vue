<template>
    <div>
        <div class="row">
            <div class="col-sm-6">
                <div class="input-group">
                    <input type="text" class="form-control" @keyup="handlePerguntaKey" v-model="pergunta" placeholder="Adicione uma nova pergunta...">
                    <span class="input-group-btn">
                        <button class="btn btn-primary" @click="handlePergunta" name="submit" value="1" type="button">
                            <i class="glyphicon glyphicon-plus"></i> Adicionar
                        </button>
                    </span>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-sm-12">
                <ul class="list-group">
                
                    <li class="list-group-item" v-for="(item, index) in items" :key="index">
                        <div class="row">
                            <div class="col-md-10">
                                {{item.pergunta}}
                            </div>

                            <div class="col-md-2 text-right div-with-download">
                                <div class="dropdown" style="display: inline" v-if="item.hasFiles == 1">
                                    <button class="btn btn-secondary dropdown-toggle" style="margin-right: 5px" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="glyphicon glyphicon-download"></i> 
                                    </button>
                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                        <a class="dropdown-item btn-download" v-for="arquivo in item.arquivos" :title="arquivo.nome" :key="arquivo.id" :href="`${baseUrl}assets/perguntas/${item.id}/${arquivo.nome}`" download>
                                            {{ arquivo.nome }}
                                        </a>
                                    </div>
                                </div>

                                <button 
                                    @click="removePergunta(index)" 
                                    class="btn btn-danger"
                                    v-if="permissao_excluir_perguntas"> 
                                    <i class="glyphicon glyphicon-trash"></i> 
                                </button>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>
<script>
import _ from 'lodash';

export default {
    data() {
        return {
            items: [],
            pergunta: '',
            permissao_excluir_perguntas: false
        }
    },
    props: {
        perguntas: {
            required: true,
            type: Array | Object
        },
        baseUrl: {
            required: true,
            type: String
        }
    },
    methods: {
        handlePerguntaKey(ev) {
            if (ev.keyCode == 13 || ev.code == 'Enter') {
                this.handlePergunta();
            }
        },
        handlePergunta() {
            if (!_.isEmpty(this.pergunta) && this.items.filter(item => item.pergunta == this.pergunta).length == 0) {
                this.items.push({
                    id: Math.random(),
                    pergunta: this.pergunta
                });
    
                this.pergunta = '';
            }
        },
        removePergunta(idItem) {
            this.items = this.items.filter((item, index) => {
                return index != idItem;
            });

            this.$emit('updatePerguntas', this.items);
        },
        pushPergunta(pergunta) {
            this.items.push(pergunta);
        },
        pushPerguntas(perguntas) {
            this.items = [];

            perguntas.map((item) => {
                this.items.push(item);
            });
        },
        getPerguntas() {
            return this.items;
        },
        async permissaoDeletarPerguntas() {
            let response = await this.$http.get('pergunta/permissaoDeletarPerguntas', {});
            this.permissao_excluir_perguntas = response.data;
        },
    },
    mounted() {
        this.items = this.perguntas;
        this.permissaoDeletarPerguntas();
    },
}
</script>

<style scoped>
 span.badge {
     cursor: pointer;
 }

 .form-group.files .form-control {
    padding: 5px 4px;
}

.btn-download {
    display: block;
    padding: 5px 12px;
    border-bottom: 1px solid rgb(211,211,211);
}

.btn-download:last-child {
    display: block;
    padding: 5px 12px;
    border-bottom: none;
}

.form-control .input-file {
    padding: 5px 5px !important;
}

.div-with-download {
    display: flex;
    justify-content: flex-end;
}
</style>