<script type="text/javascript">
    var item = {

        edit: function() {
            checked = $('input[type="checkbox"][name="item[]"]:checked');

            if (checked.length == 0) {
                swal('Atenção', 'Selecione um item para realizar a edição', 'warning');
                return false;
            }

            if (checked.length > 1) {
                swal('Atenção', 'Só é possível editar um item por vez', 'warning');
                return false;
            }

            item_part_number = encodeURIComponent($(checked).val());
            item_id_empresa = $(checked).attr('rel');
            item_estab = encodeURIComponent($(checked).attr('data-estabelecimento'));

            location.href = '<?php echo base_url() . "cadastros/mestre_itens/editar?part_number=" ?>' + item_part_number + '&id_empresa=' + item_id_empresa + '&estabelecimento=' + item_estab;
        },

        remove: function() {
            checked = $('input[type=checkbox]:checked');

            if (checked.length == 0) {
                swal('Atenção', 'Primeiro você deve selecionar o(s) íten(s) que deseja excluir.', 'warning');
                return false;
            }

            var lista_itens = new Array();

            $('input[type="checkbox"][name="item[]"]:checked').each(function() {
                var obj = {
                    'part_number': $(this).val(),
                    'estabelecimento': $(this).attr('data-estabelecimento')
                };

                lista_itens.push(obj);
            });

            var post_data = {
                'items': lista_itens
            };

            swal({
                title: "Atenção!",
                text: "Você deseja excluir os registros selecionados?",
                type: "warning",
                confirmButtonText: "OK",
                cancelButtonText: "Cancelar",
                showConfirmButton: true,
                showCancelButton: true,
                allowOutsideClick: false
            }).then(function() {
                $.post('<?php echo base_url("cadastros/mestre_itens/excluir") ?>',
                    post_data,
                    function(data, status, xhr) {
                        var json = $.parseJSON(data);

                        if (json.status == true) {
                            window.location.reload();
                        } else {
                            swal('Atenção', "Ops... um erro aconteceu, recarregue a página.", 'warning');
                        }
                    }
                );
            });
        }
    };

    $(function() {
        $('.datetimepicker').datetimepicker({
            'format': 'DD/MM/YYYY',
            'locale': 'pt-BR'
        });

    });

    $(document).ready(function() {
        $('.click-select').on('click', function(e) {
            if (e.target.nodeName != 'INPUT') {
                $(this).find('input').click();
            }
        })

        $('.btn-edit-item').on('click', function(e) {
            item.edit();
        })

        $('.btn-remove-item').on('click', function(e) {
            item.remove();
        })

        $('#btn-transferencia-owner').click(function(e) {
            $("#message_user_transfer_owner").empty();
            let checked_itens_to_owners = $('input[type="checkbox"][name="item[]"]:checked');

            if (checked_itens_to_owners.length == 0) {
                swal('Atenção', 'Selecione no mínimo um item para transferir', 'warning');
                return false;
            }

            $("#total_itens_owners").text(checked_itens_to_owners.length);
        });
    })
</script>
<link rel="stylesheet" href="<?php echo base_url('assets/vuejs/dist/atribuir-diana.css?version=' . config_item('assets_version')) ?>" type="text/css" media="screen" />

<style>
    .table.table-striped .descricao {
        width: 35% !important;
    }

    .table.table-striped .owner {
        max-width: 7% !important;
    }

    /* Toggle Switch para Triagem DIANA */
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 100%;
        margin-top: 5px;
    }

    .toggle-checkbox {
        display: none;
    }

    .toggle-label {
        display: flex;
        align-items: center;
        cursor: pointer;
        margin-bottom: 0;
        font-weight: normal;
    }

    .toggle-inner {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
        background-color: #ccc;
        border-radius: 24px;
        transition: background-color 0.3s;
        margin-right: 10px;
        flex-shrink: 0;
    }

    .toggle-inner:before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 20px;
        height: 20px;
        background-color: white;
        border-radius: 50%;
        transition: transform 0.3s;
    }

    .toggle-checkbox:checked+.toggle-label .toggle-inner {
        background-color: #CD1F73;
    }

    .toggle-checkbox:checked+.toggle-label .toggle-inner:before {
        transform: translateX(26px);
    }

    .toggle-switch-text {
        font-size: 14px;
        color: #333;
        user-select: none;
    }

    .toggle-checkbox:checked+.toggle-label .toggle-switch-text {
        color: #CD1F73;
        font-weight: 500;
    }
</style>


<div class="page-header">
    <div id="ajax_validate"></div>
    <h2>
        Mestre de Itens

        <?php if (in_array('owner', $campos_adicionais) && customer_has_role('alterar_owner',sess_user_id())) : ?>
            <button id="btn-transferencia-owner" class="btn btn-success pull-right" data-toggle="modal" data-target="#modal-transferencia-owner" style="margin-left: 5px;">Transferir Owner
                <i class="glyphicon glyphicon-user"></i>
            </button>
            <?php $this->load->view('cadastros/mestre_itens/modal-transferencia-owner'); ?>
        <?php endif; ?>
        <?php if (customer_can("importar_itens", false, false)) : ?>
            <a href="#" onclick="exportar()" id="exportar_itens" class="btn btn-success pull-right" style="margin-left: 5px">
                <i class="glyphicon glyphicon-download"></i> Exportar Itens
                <img id="loading-img-exp" src="<?php echo base_url('assets/img/loading_ajax.gif') ?>" width="10px" height="10px" style="display: none;">
            </a>
            <?php if (customer_has_role('inclusao_itens', sess_user_id())) : ?>
                <a href="<?php echo site_url("importar_itens") ?>" class="btn btn-primary pull-right" style="margin-left: 5px">
                    <i class="glyphicon glyphicon-upload"></i> Importar Itens
                </a>
            <?php endif; ?>
        <?php endif; ?>
        <?php if (customer_has_role('inclusao_itens', sess_user_id())) : ?>
            <a href="<?php echo site_url("upload") ?>" class="btn btn-default pull-right">
                <i class="glyphicon glyphicon-upload"></i> Enviar Itens Portal
            </a>
        <?php endif; ?>
        <?php if ($empresa_pais) : ?>
            <div class="dropdown pull-right" style="margin-left: 5px;right: 5px;top: -2px;">
                <button class="btn btn-success dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true" data-loading-text="Gerando...">
                    Class Países <i class="glyphicon glyphicon-cloud-download"></i>
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                    <li><a href="#" onclick="exportar_paises()" id="send_generate_log" data-loading-text="Gerando...">Exportar</a></li>
                    <li><a href="#" onclick="importar_paises()" id="send_multipaises_log" data-loading-text="Gerando...">Importar</a></li>
                    <li><a href="#" onclick="modelo_importacao_paises()" id="send_modelo_paises_log" data-loading-text="Gerando...">Modelo de Importação</a></li>
                </ul>
            </div>

        <?php endif; ?>
        <a href="#" onclick="exportar_mestre()" id="exportar_mestre" class="btn btn-success pull-right" style="margin-right: 5px">
            <i class="glyphicon glyphicon-download"></i> Exportar Mestre
            <img id="loading-img-exp-mestre" src="<?php echo base_url('assets/img/loading_ajax.gif') ?>" width="10px" height="10px" style="display: none;">
        </a>
    </h2>

    <hr />

    <div class="row">
        <div class="col-md-12">
            <?php $this->load->view('cadastros/mestre_itens/default_filter'); ?>
        </div>
    </div>
</div>
<?php if (!empty($total_rows)) : ?>
    <div class="col-md-7 d-flex result-container">
        <h2 style="margin: auto 0;">
            Resultado (<?php echo $total_rows ?>)
        </h2>
    </div>
<?php endif; ?>
<?php if (isset($pagination)) { ?>
    <div class="controls">
        <div class="pull-right">
            <?php echo (!empty($pagination) ? $pagination : '') ?>
        </div>
    </div>
<?php } ?>

<table class="table table-striped" border="0">
    <thead>
        <tr>
            <th>
                <input type="checkbox" id="toggle-checkbox" onclick="toggle_checkbox(this)" />
            </th>
            <th width="8%">Part Number</th>
            <?php if (in_array('owner', $campos_adicionais)) : ?>
                <th class="owner" width="7%" style="text-align: center; max-width: 7%">Owner</th>
            <?php else : ?>
                <th width="8%">Similar</th>
            <?php endif; ?>
            <th width="15%">Estabelecimento</th>
            <th class="descricao" style="text-align: center; min-width: 35%">Descrição</th>
            <?php if (in_array('owner', $campos_adicionais)) : ?>
                <th width="8%">Prioridade</th>
            <?php else : ?>
                <th width="8%">NCM</th>
            <?php endif; ?>
            <th width="10%">Evento</th>
            <th width="11%">Status</th>
            <th width="10%">Criação<br />Modificação</th>
            <th width="10%" class="text-center">Ficha Técnica</th>
        </tr>
    </thead>
    <tbody>
        <?php

        if (isset($list) && count($list)) {
            foreach ($list as $item) {

                $item_blqueado = false;
                $url_editar = site_url('/cadastros/mestre_itens/editar?part_number=' . urlencode($item->part_number) . '&id_empresa=' . urlencode($item->id_empresa) . '&estabelecimento=' . urlencode($item->estabelecimento));
                if (!empty($item->usuario_bloqueador) && $item->usuario_bloqueador != $user->id_usuario) {
                    $item_blqueado = true;
                    $cor_bloqueio = '#FF0000';
                    $url_editar = '#';
                } else if ($item->usuario_bloqueador == $user->id_usuario) {
                    $cor_bloqueio = '#008000';
                }

        ?>
                <tr class="click-select">

                    <td>
                        <input type="checkbox" data-toggle="true" name="item[]" <?php echo $item_blqueado ? 'disabled' : ''; ?> value="<?php echo $item->part_number ?>" rel="<?php echo $item->id_empresa ?>" data-estabelecimento="<?php echo $item->estabelecimento; ?>" />
                        <?php if (!empty($item->usuario_bloqueador)) : ?>
                            <br />
                            <i class="glyphicon glyphicon-lock" style="color:<?php echo $cor_bloqueio ?>" data-toggle="tooltip" data-placement="top" title="Bloqueado por <?php echo $item->nome_usuario_bloqueador ?>"></i>
                        <?php endif; ?>

                        <?php if ($item->sistema_origem == '' || $item->sistema_origem == 'MANUAL') : ?>
                            <span class="label label-success" data-toggle="tooltip" title="MANUAL">M</span>
                        <?php endif; ?>
                    </td>

                    <td width="8%" style="max-width: 8%; font-size:13px;">
                        <div class="d-flex">
                            <?php if (in_array('pn_primario_secundario', $campos_adicionais)) : ?>
                                <a href="<?php echo $url_editar ?>">
                                    <div style=" white-space: break-spaces;"><?php echo $item->part_number ?> </div>
                                    <?php if ($item->pn_primario_mpn != ''): ?>
                                        <div style=" white-space: break-spaces;"><?php echo $item->pn_primario_mpn ?> </div>
                                    <?php endif; ?>
                                    <?php if ($item->pn_secundario_ipn != ''): ?>
                                        <div style=" white-space: break-spaces;"><?php echo $item->pn_secundario_ipn ?> </div>
                                    <?php endif; ?>
                                </a>
                            <?php else : ?>
                                <a href="<?php echo $url_editar ?>">
                                    <div style=" white-space: break-spaces;"><?php echo $item->part_number ?> </div>
                                </a>
                            <?php endif; ?>
                            <?php if (in_array('item_novo_ou_modificado', $campos_adicionais)) : ?>
                                <?php echo !empty($item->integracao_novo_material) ? '(' . $item->integracao_novo_material . ')&nbsp;' : ''; ?>
                            <?php endif; ?>
                            <?php if ($item->item_ja_homologado == 1) : ?>
                                <span data-toggle="tooltip" title="Item já homologado anteriormente" style="margin-left: 6px;">
                                    <img src="/assets/img/ok-icon.ico" alt="Ícone check verde para representar OK" width="16">
                                </span>
                            <?php endif; ?>
                        </div>
                        <br>
                        <?php if ($has_status_exportacao) : ?>
                            <?php if ($item->status_exportacao) : ?>
                                <span style="color: white;background-color:#5cb85c; margin-bottom: 2px;" class="badge" data-toggle="tooltip" title="Item exportado">
                                    <small>Exportado</small>
                                </span>
                            <?php else : ?>
                                <span style="color: white;background-color:#af0000; margin-bottom: 2px;" class="badge" data-toggle="tooltip" title="Item pendente">
                                    <small>Pendente</small>
                                </span>
                            <?php endif; ?>
                        <?php endif; ?>
                        <?php if (in_array('owner', $campos_adicionais)) : ?>
                            <?php if ($item->status_formatado == 'Homologado em Revisão') : ?>
                                <span style="color: white;background-color:#af0000;" class="badge" data-toggle="tooltip" title="Item Pendente de Integração">
                                    <small>Pendente Integração</small>
                                </span>
                            <?php endif; ?>
                        <?php endif; ?>
                        <?php if (!empty($item->wf_id)) : ?>
                            <span class="label" <?php echo "style=' background-color: $item->wf_color; color: #fff;' " ?> data-toggle="tooltip" title="<?php echo $item->wf_status_atributos ?>" style="margin-top:5px"><?php echo $item->wf_status_atributos ?></span>
                        <?php endif; ?>
                        <br>
                        <?php if (in_array('status_triagem_diana', $funcoes_adicionais)) : ?>
                            <?php if ($item->status_triagem_diana == 'Pendente de triagem' && $item->id_status == 6) : ?>
                                <span class="label label-warning">Pendente de triagem</span>
                            <?php endif; ?>
                            <?php if ($item->status_triagem_diana == 'Triagem aprovada' && (($item->id_status == 6) || ($item->id_status == 8))) : ?>
                                <span class="label label-success">Triagem aprovada</span>
                            <?php endif; ?>
                            <?php if ($item->status_triagem_diana == 'Triagem reprovada' && ($item->id_status == 7)) : ?>
                                <span class="label" style="background-color: #B01A00">Triagem reprovada</span>
                            <?php endif; ?>
                            <?php if ($item->status_triagem_diana == 'Falha na triagem' && ($item->id_status == 8)) : ?>
                                <span class="label" style="background-color: #CD1F73">Falha na triagem</span>
                            <?php endif; ?>
                        <?php endif; ?>
                    </td>
                    <?php if (in_array('owner', $campos_adicionais)) : ?>
                        <td class="owner" width="7%" style="font-size: 13px; max-width: 7%;">
                            <?php echo $item->owner_codigo ?> - <?php echo $item->owner_descricao ?> - <?php echo $item->responsaveis_gestores_nomes ?>
                        </td>
                    <?php else : ?>
                        <td><?php echo $item->part_number_similar ?></td>
                    <?php endif; ?>

                    <td><?php echo !empty($item->estabelecimento) ? $item->estabelecimento : 'N/A' ?></td>

                    <?php if ($hasDescricaoGlobal) : ?>
                        <td class="descricao" width="35%" style="min-width: auto;">

                            <?php
                            $textoDescricao = (strlen($item->descricao) > 30) ? substr($item->descricao, 0, 30) . "..." : $item->descricao;
                            $textoDescricaoGlobal = (strlen($item->descricao_global) > 30) ? substr($item->descricao_global, 0, 30) . "..." : $item->descricao_global;
                            echo (!empty($item->descricao)) ? $textoDescricao :
                                $textoDescricaoGlobal . "<strong> (GLOBAL)</strong>"  ?>

                        </td>
                    <?php else :
                        $textoDescricao = (strlen($item->descricao) > 30) ? substr($item->descricao, 0, 30) . "..." : $item->descricao;
                    ?>
                        <td class="descricao" width="35%" style="min-width: auto;"><?php echo $textoDescricao ?></td>
                    <?php endif; ?>

                    <?php if (in_array('owner', $campos_adicionais)) : ?>
                        <td><?php echo $item->empresa_prioridade ?></td>
                    <?php else : ?>
                        <td><?php echo $item->ncm ?></td>
                    <?php endif; ?>

                    <td width="8%" style="max-width: 8%;"><?php echo $item->evento ?></td>

                    <td><?php echo $item->status_formatado == 'Homologar' ? 'Pendente de Homologação' : $item->status_formatado ?></td>

                    <td width="10%" style="font-size: 13px;">
                        <?php
                        $dat_criacao = '';
                        $data_modificacao = '';
                        if (!empty($item->dat_criacao)) {
                            $dat_criacao = date("d/m/Y", strtotime($item->dat_criacao));
                            echo '<strong>DC:' . $dat_criacao . '</strong>';
                        }
                        if (!empty($item->data_modificacao)) {
                            $data_modificacao = date("d/m/Y", strtotime($item->data_modificacao));
                            echo '<br/><strong>DM:' . $data_modificacao . '</strong>';
                        }
                        ?>
                    </td>

                    <td class="text-center">
                        <?php
                        $ficha_tecnica = $this->anexo_model->get_entries($item->part_number, $item->estabelecimento, $item->id_empresa);
                        if (count($ficha_tecnica)) :
                            $arquivo = current($ficha_tecnica);
                            $download_url = site_url('download?arquivo=' . $arquivo->nome_hash); ?>
                            <a href="<?php echo $download_url ?>" class="btn btn-sm btn-default"><i class="glyphicon glyphicon-cloud-download"></i> Baixar</a>
                        <?php else : ?>
                            -
                        <?php endif; ?>
                    </td>
                </tr>
        <?php
            }
        }

        ?>
    </tbody>
</table>

<?php if (isset($pagination)) { ?>
    <div class="controls">
        <div class="pull-right">
            <?php echo (!empty($pagination) ? $pagination : '') ?>
        </div>
    </div>
<?php } ?>

<script>
    $(document).ready(function() {
        $('#collapseOne').collapse('hide');

        if ($('#novo_material_modal').val() != '-1' || ($('#estabelecimento_modal').val().length != 0 && $('#estabelecimento_modal').val() != '-1') || ($('#ncm_proposta_modal').val().length != 0 && $('#ncm_proposta_modal').val() != '-1') || ($('#ex_ipi_modal').val().length != 0 && $('#ex_ipi_modal').val() != '-1') || ($('#ex_ii_modal').val().length != 0 && $('#ex_ii_modal').val() != '-1') || ($('#sistema_origem_modal').val().length != 0 && $('#sistema_origem_modal').val() != '-1') || $('#descricao_completa_modal').val() != '' || $('#descricao_global_modal').val() != '' || $('#data_inicio_modificacao_modal').val() != '' || $('#data_fim_modificacao_modal').val() != '') {

            $('#collapseOne').collapse('show');
        }
    });


    function exportar() {

        var botaoExportar = document.getElementById("exportar_itens");
        botaoExportar.setAttribute("disabled", "disabled");
        $('#loading-img-exp').show();
        $('#loading-overlay').show();
        var form = $("#search_form_id");
        $.ajax({
            type: 'post',
            url: "<?php echo site_url('importar_itens/exportar') ?>",
            data: form.serialize(),
            xhrFields: {
                responseType: 'blob'
            },
            success: function(response, status, xhr) {
                botaoExportar.removeAttribute("disabled");
                $('#loading-img-exp').hide();
                $('#loading-overlay').hide();
                var contentType = xhr.getResponseHeader('Content-Type');
                if (contentType.indexOf('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') !== -1) {
                    var blobUrl = URL.createObjectURL(response);

                    var linkDownload = document.createElement('a');
                    linkDownload.href = blobUrl;
                    linkDownload.download = 'itens.xlsx';
                    linkDownload.click();
                }
            },
            complete: function() {
                $('#loading-overlay').hide();
            },
            error: function(xhr, status, error) {
                $('#loading-overlay').hide();
            }
        });
    }

    function exportar_mestre() {
        var botaoExportar = document.getElementById("exportar_mestre");
        var botaoPesquisar = document.getElementById("pesquisar-btn");
        botaoExportar.setAttribute("disabled", "disabled");
        $('#loading-img-exp').show();
        $('#loading-overlay').show();
        var form = $("#search_form_id");
        form.attr("action", "<?php echo site_url('exportar_itens/exportar_mestre') ?>");

        $.ajax({
            type: form.attr('method'),
            url: form.attr('action'),
            data: form.serialize(),
            xhrFields: {
                responseType: 'blob'
            },
            success: function(response) {

                if (response instanceof Blob) {
                    var blobUrl = window.URL.createObjectURL(response);

                    var linkDownload = document.createElement('a');
                    linkDownload.href = blobUrl;
                    linkDownload.download = 'mestre_itens.xlsx';

                    document.body.appendChild(linkDownload);
                    linkDownload.click();
                    document.body.removeChild(linkDownload);

                    window.URL.revokeObjectURL(blobUrl);
                } else {
                    $('#loading-overlay').hide();
                }
            },
            error: function(xhr, status, error) {
                console.error('Erro na requisição:', error);
            },
            complete: function() {
                botaoExportar.removeAttribute("disabled");
                botaoPesquisar.removeAttribute("disabled");
                $('#loading-img-exp').hide();
                $('#loading-img').hide();
                $('#loading-overlay').hide();
                form.attr("action", "<?php echo site_url('cadastros/mestre_itens?search=') ?>");
            }
        });
    }

    function upload() {
        $('#loading-overlay').show();
        var form = $("#search_form_id");
        form.attr("action", "<?php echo site_url('upload') ?>");
        form.submit();
    }

    function importar_paises() {
        $('#loading-overlay').show();
        var form = $("#search_form_id");
        form.attr("action", "<?php echo site_url('importar_paises') ?>");
        form.submit();
    }

    function exportar_paises() {
        $('#loading-overlay').show();
        var botaoExportar = document.getElementById("send_generate_log");
        botaoExportar.setAttribute("disabled", "disabled");
        $('#loading-img-exp').show();

        var form = $("#search_form_id");
        $.ajax({
            type: 'post',
            url: "<?php echo site_url('importar_paises/exportar') ?>",
            data: form.serialize(),
            xhrFields: {
                responseType: 'blob'
            },
            success: function(response, status, xhr) {
                var contentType = xhr.getResponseHeader('Content-Type');
                if (contentType.indexOf('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') !== -1) {
                    var blobUrl = URL.createObjectURL(response);

                    var linkDownload = document.createElement('a');
                    linkDownload.href = blobUrl;
                    linkDownload.download = 'classificacao_paises.xlsx';
                    document.body.appendChild(linkDownload);
                    linkDownload.click();
                    document.body.removeChild(linkDownload);
                }

                botaoExportar.removeAttribute("disabled");
                $('#loading-img-exp').hide();
            },
            error: function(xhr, status, error) {
                $('#loading-overlay').hide();
                botaoExportar.removeAttribute("disabled");
                $('#loading-img-exp').hide();
            },
            complete: function() {
                $('#loading-overlay').hide();
            }
        });
    }


    function modelo_importacao_paises() {
        $('#loading-overlay').show();
        var botaoExportar = document.getElementById("send_generate_log");
        botaoExportar.setAttribute("disabled", "disabled");
        $('#loading-img-exp').show();

        var form = $("#search_form_id");
        $.ajax({
            type: 'post',
            url: "<?php echo site_url('importar_paises/exportar_modelo') ?>",
            data: form.serialize(),
            xhrFields: {
                responseType: 'blob'
            },
            success: function(response, status, xhr) {
                botaoExportar.removeAttribute("disabled");

                $('#loading-img-exp').hide();

                var contentType = xhr.getResponseHeader('Content-Type');
                if (contentType.indexOf('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') !== -1) {
                    var blobUrl = URL.createObjectURL(response);

                    var linkDownload = document.createElement('a');
                    linkDownload.href = blobUrl;
                    linkDownload.download = 'modelo_importacao_paises.xlsx';
                    linkDownload.click();
                }
            },
            complete: function() {
                $('#loading-overlay').hide();
            }
        });
    }
</script>
