<div id="modalAnteriorWrapper">

    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <div class="row">
                    <ul class="nav nav-tabs" id="myTab" role="tablist">
                        <li class="nav-item" style="margin-left: 15px;">
                            <a class="nav-link active" id="home-tab" data-toggle="tab" href="#home" role="tab" aria-controls="home" aria-selected="true">Detalhes do Item</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="profile-tab" data-toggle="tab" href="#profile" role="tab" aria-controls="profile" aria-selected="false">Perguntas e Respostas</a>
                        </li>
                        <?php if (in_array('usuario_seguidor', $campos_adicionais)) : ?>
                        <li class="nav-item" id="seguidores-nav">
                            <a class="nav-link" id="seguidores-tab" data-toggle="tab" href="#seguidores" role="tab" aria-controls="seguidores" aria-selected="false">Usuários Seguidores</a>
                        </li>
                    <?php endif; ?>
                </ul>

                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane active" id="home" role="tabpanel" aria-labelledby="home-tab">
                            <?php if (isset($item) && !empty($item)) : ?>
                                <div class="modal-body">
                                    <table class="table table-striped">
                                        <tr>
                                            <td><strong>Função:</strong></td>
                                            <td><?php echo empty($item->funcao) ? '-' : $item->funcao ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Peso:</strong></td>
                                            <td><?php echo empty($item->peso) ? '-' : $item->peso ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Aplicação:</strong></td>
                                            <td><?php echo empty($item->aplicacao) ? '-' : $item->aplicacao ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Informações Adicionais:</strong></td>
                                            <td><?php echo empty($item->inf_adicionais) ? '-' : $item->inf_adicionais ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Marca:</strong></td>
                                            <td><?php echo empty($item->marca) ? '-' : $item->marca ?></td>
                                        </tr>
                                         <?php if (in_array('owner', $campos_adicionais) && isset($item->cod_owner)) : ?>
                                            <tr>
                                                <td><strong>Owner:</strong></td>
                                                <td><?php echo empty($item->cod_owner) ? '-' : $item->cod_owner . ' - ' . (!empty($owner_atual) ? $owner_atual[0]->descricao : '') ?></td>
                                            </tr>
                                        <?php endif; ?>
                                        <tr>
                                            <td><strong>Material Constitutivo:</strong></td>
                                            <td><?php echo empty($item->material_constitutivo) ? '-' : $item->material_constitutivo ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Origem (País):</strong></td>
                                            <td><?php echo empty($item->origem) ? '-' : $item->origem ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Máquina:</strong></td>
                                            <td><?php echo empty($item->maquina) ? '-' : $item->maquina ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Memória de Classificação:</strong></td>
                                            <td><?php echo empty($item->memoria_classificacao) ? '-' : $item->memoria_classificacao ?></td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <strong>Resumo: </strong>
                                            </td>
                                            <td>
                                                <?php echo !empty($item->descricao_proposta_completa) ? $item->descricao_proposta_completa : '-' ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <strong>Observações: </strong>
                                            </td>
                                            <td>
                                                <?php echo !empty($item->observacoes) ? $item->observacoes : '-' ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <strong>Dados COMEX: </strong>
                                            </td>
                                            <td>
                                            INDICADOR COMEX: <?php echo !empty($item->ind_ecomex) ? $item->ind_ecomex : '-' ?> //&nbsp;
                                            DI: <?php echo !empty($item->num_di) ? $item->num_di : '-' ?>//&nbsp;
                                            DATA DA ÚLTIMA DI: <?php
                                                $dt_ultima_di = '';
                                                if (!empty($item->data_di)) {
                                                    $dt_ultima_di = date("d/m/Y", strtotime($item->data_di));
                                                    echo $dt_ultima_di;
                                                }else{
                                                    echo '-';
                                                }
                                                ?>//&nbsp;
                                            DRAWBACK (S OU N): <?php echo !empty($item->ind_drawback) && $item->ind_drawback == 'S' ? 'S' : 'N' ?> //&nbsp;
                                            NCM COMEX:  <?php echo !empty($item->ncm) ? $item->ncm : '-' ?>&nbsp;
                                                
                                            </td>
                                        </tr>
                                        <?php if ($can_ncm_fornecedor) : ?>
                                            <tr>
                                                <td><strong>NCM Fornecedor:</strong></td>
                                                <td><?php echo empty($item->ncm_fornecedor) ? '-' : $item->ncm_fornecedor; ?></td>
                                            </tr>
                                        <?php endif; ?>
                                        <tr>
                                            <td><strong>Evento:</strong></td>
                                            <td><?php echo empty($item->evento) ? '-' : $item->evento ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Descrição:</strong></td>
                                            <td><?php echo empty($item->descricao) ? '-' : $item->descricao ?></td>
                                        </tr>
                                        
                                        <?php if (in_array('descricao_global', $campos_adicionais) && !empty($item->descricao_global)) : ?>
                                            <tr>
                                                <td><strong>Descrição Global:</strong></td>
                                                <td><?php echo empty($item->descricao_global) ? '' : $item->descricao_global ?></td>
                                            </tr>
                                        <?php endif; ?>

                                        <tr>
                                            <td><strong>Status Formatado:</strong></td>
                                            <td><?php echo empty($item->status_formatado) ? '-' : $item->status_formatado ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Ficha Técnica:</strong></td>
                                            <td>
                                                <?php
                                                $ficha_tecnica = $this->anexo_model->get_entries($item->part_number, $item->estabelecimento, $item->id_empresa);
                                                if (count($ficha_tecnica)) :
                                                    $arquivo = current($ficha_tecnica);
                                                    $download_url = site_url('download?arquivo=' . $arquivo->nome_hash); ?>
                                                    <a href="<?php echo $download_url ?>" class="btn btn-sm btn-default"><i class="glyphicon glyphicon-cloud-download"></i> Baixar</a>
                                                <?php else : ?>
                                                    -
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    </table>

                                </div>
                            <?php else : ?>
                                <div class="modal-body" style="margin: 15px;">
                                    <p><strong>Não foi encontrada nenhuma informação.</strong></p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                            <div id="app-listagem-perguntas-respostas" class="col-sm-12" style="margin-top: 15px;">
                                <v-listagem-perguntas-respostas partnumber="<?php echo !empty($item->part_number) ? $item->part_number : '' ?>" estabelecimento="<?php echo !empty($item->estabelecimento) ? $item->estabelecimento : '' ?>" base-url="<?php echo base_url() ?>"></v-listagem-perguntas-respostas>
                            </div>

                            <script type="text/javascript" src="<?php echo base_url('assets/vuejs/dist/listagem-perguntas-respostas.js?version=' . config_item('assets_version')) ?>"></script>
                            <link rel="stylesheet" href="<?php echo base_url('assets/vuejs/dist/listagem-perguntas-respostas.css?version=' . config_item('assets_version')) ?>" type="text/css" media="screen" />
                            
                        </div>
                        <div class="tab-pane fade" id="seguidores" role="tabpanel" aria-labelledby="seguidores-tab">
                            <?php $this->load->view('atribuir_grupo/modal-usuarios-seguidores') ?>
                        </div>
                    </div>

                </div>
            </div>
   

            <div class="modal-footer" id="main-modal-footer">
            <?php $has_permission = customer_has_role('devolver_pn_ajuste_descricao_setor', sess_user_id()); ?>

 
                <?php if ((in_array('revisar_info_erp', $campos_adicionais)) && (in_array('unidade_negocio', $campos_adicionais)) 
                    && ($item->status_formatado == 'Informações ERP Revisadas' || $item->status_formatado == 'Perguntas Respondidas' || $item->status_formatado == 'Perguntas Respondidas (Novas)')) : ?>
                    
                    <?php if ( (!$has_permission && $item->status_formatado == 'Perguntas Respondidas' || $item->status_formatado == 'Perguntas Respondidas (Novas)') || $has_permission) : ?>
                    
                    <button type="button" class="btn btn-primary" id="revisar_info_erp">
                        <i class="glyphicon glyphicon-question-sign"></i> Revisar Informações ERP
                    </button>
                    <?php endif; ?>
                <?php endif; ?>

                <button type="button" class="btn btn-default" data-dismiss="modal">Fechar</button>

                <?php if ($perguntasPendentes) : ?>
                    <?php $item_pergunta = htmlspecialchars($item->part_number.'|'.urlencode($item->estabelecimento)); ?>
                    <a href="<?php echo base_url("controle_pendencias/responder?partnumber=$item_pergunta&responder=1&clean_bulk=1"); ?>" class="btn btn-primary btn-responder-modal-detalhes" title="Responder pergunta relacionadas ao Part Number">Responder</a>
                <?php endif; ?>

                <button type="button" class="btn btn-primary btn-nova-pergunta-modal-detalhes">
                    <i class="glyphicon glyphicon-question-sign"></i> Nova Pergunta
                </button>
            </div>
        </div>
    </div>
</div>

<?php $this->load->view('atribuir_grupo/modal-revisar-info-erp'); ?>

<script type="text/javascript">
    // Função para verificar a guia ativa e exibir ou ocultar o modal-footer
    function toggleModalFooter() {
        if ($('#seguidores-nav').hasClass('active')) {
            $('#main-modal-footer').hide();
        } else {
            $('#main-modal-footer').show();
        }
    }
    // Verifique a guia ativa e exiba ou oculte o modal-footer ao carregar a página
    toggleModalFooter();
    // Verifique a guia ativa e exiba ou oculte o modal-footer quando uma guia for clicada
    $('#myTab .nav-link').on('click', function() {
        // Espere que o Bootstrap altere a classe 'active' antes de verificar novamente
        setTimeout(toggleModalFooter, 100);
    });

    $("#home-tab").tab('show');

    $(function() {
        $(".btn-responder-modal-detalhes").hide();
        $(".btn-nova-pergunta-modal-detalhes").hide();
    });

    $(".btn-nova-pergunta-modal-detalhes").on("click", function() {
        localStorage.setItem('infoModalPerguntas', "<?php echo $item->part_number; ?>");
        localStorage.setItem('infoModalPerguntasEstabelecimento', "<?php echo $item->estabelecimento; ?>");
        $("#detalhes-part-number").modal("hide");
        $("#perguntasRespostas").modal("show");
    });

    $("#detalhes-part-number").on("hide.bs.modal", function() {
        $(".btn-responder-modal-detalhes").hide();
        $(".btn-nova-pergunta-modal-detalhes").hide();
    });

    $("#profile-tab").on("show.bs.tab", function() {
        $(".btn-responder-modal-detalhes").show(150);
        $(".btn-nova-pergunta-modal-detalhes").show(150);
        $("#revisar_info_erp").hide();
    });

    $("#profile-tab").on("hide.bs.tab", function() {
        $(".btn-responder-modal-detalhes").hide(150);
        $(".btn-nova-pergunta-modal-detalhes").hide(150);
        $("#revisar_info_erp").show();
    });

    $.ajax({
        url: '<?php echo base_url('pr/perguntas/getUsuarioBloqueador');?>',
        method: 'GET',
        data: {
            part_number: '<?php echo $item->part_number; ?>',
            estabelecimento: '<?php echo $item->estabelecimento; ?>'
        },
        success: (data) => {
            console.log(data);
            if(data.error){
                $(".btn-responder-modal-detalhes").attr('disabled', 'disabled');
                $(".btn-nova-pergunta-modal-detalhes").attr('disabled', 'disabled');
            }
        },
        error: (error) => {
            console.error('Ocorreu um erro:', error);
        }
    });

    // $('#revisar_info_erp').click(function() {
    //     $('#modalRevisarInfoERP').modal('show');

    //     setTimeout(function() {
    //         $('#detalhes-part-number').modal('hide');
    //     }, 1500); // Ajuste o tempo de atraso, se necessário
    // });

    var modalAnteriorWrapper = $('#modalAnteriorWrapper');
    var modalAnteriorContent = modalAnteriorWrapper.html();

    $('#revisar_info_erp').click(function() {
        modalAnteriorWrapper.empty(); // Remover o modal anterior do DOM
        $('#modalRevisarInfoERP').modal('show');
    });
</script>