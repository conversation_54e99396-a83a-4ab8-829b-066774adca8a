<style type="text/css" media="screen">
    .borderless td,
    .borderless th {
        border: 0 !important;
    }

    table th {
        text-transform: uppercase;
    }

    /* O seletor com vírgula aplica os estilos tanto para inputs quanto para selects */
    .editable-input input,
    .editable-input select {
        width: 950px !important;
        box-sizing: border-box !important;
    }

    #header-container {
        display: flex;
        justify-content: space-between;
        gap: 10px;
    }

    #header-container #header-container-buttons {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 10px;
    }

    #header-container #header-container-buttons #header-container-buttons-first-line {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        justify-content: flex-end;
    }

    #header-container #header-container-buttons #header-container-buttons-second-line {
    }

    /* Os media queries também precisam incluir ambos os elementos */
    @media screen and (max-width: 1199px) {
        #header-container {
            flex-direction: column;
        }
    }

    @media screen and (max-width: 1024px) {
        .editable-input input,
        .editable-input select {
            width: 90vw !important;
            max-width: 950px !important;
        }
    }

    @media screen and (max-width: 768px) {
        .editable-input input,
        .editable-input select {
            width: 95vw !important;
            max-width: 950px !important;
        }
    }

    @media screen and (max-width: 480px) {
        .editable-input input,
        .editable-input select {
            width: 98vw !important;
            padding: 0 10px !important;
        }
    }
</style>

<?php
$fotos = $this->foto_model->get_fotos($part_number, $item->id_empresa, NULL, NULL, $item->estabelecimento);
$has_fotos = (count($fotos) > 0) ? TRUE : FALSE;

$total_pendencias = 0;

if (empty($item->num_ex_ii) && !empty($ex_tarifarios_ii) && customer_can('ii')) {
    $total_pendencias++;
}

if (empty($item->num_ex_ipi) && !empty($ex_tarifarios_ipi) && customer_can('ipi')) {
    $total_pendencias++;
}

if (empty($item->cod_cest_proposto) && !empty($cests) && customer_can('cest')) {
    $total_pendencias++;
}

if (!empty($item->ncm_proposto)) {
    $atributos_ncm = $this->nve_atributo_model->get_atributos_by_ncm($item->ncm_proposto);
} else {
    $atributos_ncm = NULL;
}

if (!empty($atributos_ncm) && $has_nve === FALSE && customer_can('nve')) {
    $total_pendencias++;
}

if ($existe_suframa_ncm_produto && customer_can('suframa')) {
    $total_pendencias++;
}

if (customer_can('classificacao_energetica', false, false)) {
    if (empty($item->id_classificacao_energetica)) {
        if ($this->ncm_model->ncm_has_classificacao_energetica($item->ncm_proposto)) {
            $total_pendencias++;
        }
    }
}

if ($has_attrs && $has_attrs_pendencias) {
    $total_pendencias++;
    // if (in_array("integracao_ecomex", $funcoes_adicionais))
    // {
    //     if ($item->indicador_ecomex == 'EI')
    //     {
    //         $total_pendencias++;
    //     }
    // } else {
    //     $total_pendencias++;
    // }
}
?>



<script type="text/javascript">
    function listar_fotos(that) {
        $(that).hide();
        $('#lista-fotos').show();
    }

    function update_info(e) {
        var id_item = '<?php echo $item->id_item ?>';
        var url = '<?php echo site_url("homologacao/get_ex_ii_ipi_by_id_item"); ?>/' + id_item;
        $.ajax({
            url: url,
            success: function(data) {
                $('#tr_item_num_ex_ii').html('');
                $('#tr_item_num_ex_ipi').html('');
                $('#aliquotas_ex').html('');
                var aliquota_ex = '';
                if (data) {
                    if ("item_ex_ii" in data) {
                        var item_ex_ii = data.item_ex_ii;
                        var html = '<th width="30%" class="text-right">EX de II:</th>';
                        html += '<td>' + item_ex_ii.descricao_linha1;
                        if (item_ex_ii.pct_ii != null && item_ex_ii.pct_ii != undefined) {
                            aliquota_ex = '<strong> II: </strong><span class="red">' + item_ex_ii.pct_ii + '%</span>';
                        }
                        html += '</td>';
                        $('#tr_item_num_ex_ii').html(html);
                    }
                    if ("item_ex_ipi" in data) {
                        var item_ex_ipi = data.item_ex_ipi;
                        var html = '<th width="30%" class="text-right">EX de IPI:</th>';
                        html += '<td>';
                        html += 'EX ' + item_ex_ipi.num_ex + ' - ';
                        html += item_ex_ipi.descricao_linha1;
                        if (item_ex_ipi.pct_ipi != null && item_ex_ipi.pct_ipi != undefined) {
                            aliquota_ex += '<strong> IPI: </strong><span class="red">' + item_ex_ipi.pct_ipi + '%</span>';
                        }
                        html += '</td>';
                        $('#tr_item_num_ex_ipi').html(html);
                    }

                    if (aliquota_ex != '') {
                        aliquota_ex = 'ALÍQUOTA DE EXCEÇÃO ' + aliquota_ex;
                        var html_aliquota = '<th width="30%" class="text-right">&nbsp;</th>';
                        html_aliquota += '<td><small>' + aliquota_ex + '</small></td>';
                        $('#aliquotas_ex').html(html_aliquota);
                    }
                }
            }
        })
    };

    $(function() {
        <?php

        if ($part_number_similar && $has_fotos && !$has_ctr_fotos) {
        ?>
            $('#lista-fotos').hide();
        <?php
        }
        ?>

        $("a.display-ncm-table").click(function() {
            var toggle = '#' + $(this).attr('data-toggle');

            if ($(toggle).hasClass('hide')) {
                $(toggle).removeClass('hide');
            } else {
                $(toggle).addClass('hide');
            }
        });

        $("a.display-obs").click(function() {
            var toggle = '#' + $(this).attr('data-toggle');

            if ($(toggle).hasClass('hide')) {
                $(toggle).removeClass('hide');
            } else {
                $(toggle).addClass('hide');
            }
        });
    });

    $(function() {

        $(".save_changes_cest").on("click", function() {
            $('#validate_cest_response').html('');

            var type = $(this).attr('data-type');
            var cest = $('#' + type).val();

            if (cest == null) {
                $('#validate_cest_response').html('<div class="alert alert-danger"><strong>Oops!</strong> Selecione o CEST que deseja vincular ao item.</div>');
                return false;
            }

            var id_item = $(this).attr('data-id');

            var post_data = {
                'id_item': id_item,
                'cest': cest,
                'type_cest': type
            };

            $.post('<?php echo site_url("homologacao/ajax_save_cest"); ?>', post_data, function(data) {
                if (data == 1) {
                    $('#validate_cest_response').html('<div class="alert alert-success"><strong>OK!</strong> CEST salvo com sucesso.</div>');
                }
            });
        });

        <?php if (company_can("lessin")) : ?>
            $("#save-lessin").on("click", function() {
                const data = {
                    'partnumber': '<?php echo $item->part_number ?>',
                    'estabelecimento': '<?php echo $item->estabelecimento ?>'
                };

                $("#lessin-load-html").html("<p>Carregando...</p>");


                $.post('<?php echo site_url("homologacao/ajax_save_lessin"); ?>', data).done(function(data) {
                    if (!data.error) {
                        $("#lessin-load-html").html(data.html)

                        return $('#validate-lessin').html('<div class="alert alert-success"><strong>OK!</strong> Item LESSIN salvo com sucesso.</div>');
                    } else {
                        $("#lessin-load-html").html("<p>Ocorreu um problema ao atualizar.</p>");
                        $('#validate-lessin').html('<div class="alert alert-danger"><strong>Ops...</strong> Não foi possível salvar item LESSIN.</div>');
                    }
                });
            });
        <?php endif; ?>

        $('.save_change_classificacao_energetica').on("click", function() {
            $('#validate_classificacao_energetica_response').html('');

            var type = $(this).attr('data-type');
            var classificacao_energetica = $('#' + type).val();

            if (classificacao_energetica == null) {
                $('#validate_classificacao_energetica_response').html('<div class="alert alert-danger"><strong>Oops!</strong> Selecione a Classficicação que deseja vincular ao item.</div>');
                return false;
            }

            var id_item = $(this).attr('data-id');

            var post_data = {
                'id_item': id_item,
                'classificacao_energetica': classificacao_energetica,
                'type_classificacao_energetica': type
            };

            $.post('<?php echo site_url("homologacao/ajax_save_classificacao_energetica"); ?>', post_data, function(data) {
                if (data.message == 'Sucesso') {
                    $('#validate_classificacao_energetica_response').html('<div class="alert alert-success"><strong>OK!</strong> Classificação Energética salva com sucesso.</div>');
                }
            });
        })

        $('.save_changes').on('click', function() {
            $('#validate_ex_response').html('');

            var type = $(this).attr('data-type');
            var num_ex = $('#' + type).val();

            if (num_ex == null) {
                $('#validate_ex_response').html('<div class="alert alert-danger"><strong>Oops!</strong> Selecione o EX Tarifário que deseja vincular ao item.</div>');
                return false;
            }

            var id_item = $(this).attr('data-id');

            var post_data = {
                'id_item': id_item,
                'num_ex': num_ex,
                'type_ex': type
            };

            $.post('<?php echo site_url("homologacao/ajax_save_ex_tarifario"); ?>', post_data, function(data) {
                if (data == 1) {
                    $('#validate_ex_response').html('<div class="alert alert-success"><strong>OK!</strong> EX Tarifário salvo com sucesso.</div>');
                    update_info();
                }
            });
        });

        $('.atribuir_save_suframa').on('click', function() {
            var button = $(this);
            var selectedItem = $('.suframa-destaque-tbody input:checked');
            var codigoProduto = $('#suframa-produto-select').val();

            var id_item = '<?php echo $item->id_item ?>';

            var ppbData = selectedItem.data('ppb');
            var ppbValue = ppbData ? ppbData.replace(/&nbsp;/, "").trim() : "";

            var post_data = {
                'id_item': id_item,
                'suframa_val': 1,
                'destaque': selectedItem.data('destaque'),
                'ppb': ppbValue,
                'codigo': selectedItem.data('codigo'),
                'descricao': selectedItem.data('descricao'),
                'produto': selectedItem.data('produto')
            };

            button.attr('disabled', 'disabled');
            button.text('Atribuindo, aguarde...');

            $.post('<?php echo site_url("homologacao/ajax_save_suframa"); ?>', post_data, function(data) {
                if (data && data.message == 'Sucesso') {
                    swal({
                        type: 'success',
                        text: 'Suframa vinculado com sucesso'
                    }).then(function() {
                        location.reload();
                    });
                } else {
                    swal({
                        type: 'error',
                        text: 'Ocorreu um erro ao vincular SUFRAMA'
                    });
                }

                button.removeAttr('disabled');
                button.text('Atribuir novo SUFRAMA');
            });
        });

        $('.desvincular_save_suframa').on('click', function() {
            var suframa_val = -1;

            if (!suframa_val) return false;

            var id_item = '<?php echo $item->id_item ?>';

            var post_data = {
                'id_item': id_item,
                'suframa_val': suframa_val,
            };

            $.post('<?php echo site_url("homologacao/ajax_save_suframa"); ?>', post_data, function(data) {
                if (data && data.message == 'Sucesso') {
                    swal({
                        type: 'success',
                        text: 'Suframa desvinculado com sucesso'
                    }).then(function() {
                        location.reload();
                    });
                } else {
                    swal({
                        type: 'error',
                        text: 'Ocorreu um erro ao desvincular SUFRAMA'
                    });
                }
            });
        });

        $('#tabs_homolog a').click(function(e) {
            e.preventDefault()
            $(this).tab('show')
        });

    });
</script>


<div class="page-header">
    <h3 id="header-container">
        Ficha de Homologação
        <div id="header-container-buttons">
            <div id="header-container-buttons-first-line">
                <?php if (in_array('owner', $campos_adicionais)) : ?>
                    <?php if ($item->status_slug == 'homologado_em_revisao') : ?>
                        <button class="btn btn-success" id="revisarItem" title="Clique para confirmar a revisão do item">
                            <img src="<?php echo base_url('assets/img/check-all.svg') ?>" alt="">
                            Item Revisado
                        </button>
                    <?php endif ?>
                <?php endif ?>

                <?php if (has_role('admin') || has_role('consultor')) : ?>
                    <?php if ($item->status_slug != 'homologar') : ?>
                        <button class="btn btn-success" id="pendenteHomologacao" title="Clique para confirmar o envio para Pendente de Homologação">
                            <i class="glyphicon glyphicon-check"></i>
                            Pendente de Homologação
                        </button>
                    <?php endif ?>
                <?php endif ?>

                <?php if ($simplus) : ?>
                    <button class="btn btn-success btn-modal-simplus" href="<?php echo site_url("homologacao/modal_simplus") ?>" title="Envia o item para a Simplus"><i class="glyphicon glyphicon-retweet"></i> Enviar para a Simplus</button>
                <?php else : ?>
                    <button class="btn btn-success" data-toggle="modal" data-target="#transferencia-modal" title="Transfira o responsável pelo item"><i class="glyphicon glyphicon-user"></i> Transferir responsável</button>
                <?php endif ?>
                <?php if (in_array('usuario_seguidor', $campos_adicionais)) : ?>
                    <button class="btn btn-primary" data-toggle="modal" data-target="#seguidores-modal" title="Adicione usuários que irão acompanhar o item"><i class="glyphicon glyphicon-user"></i> Usuários Seguidores</button>
                    <?php $this->load->view('homologacao/modal_usuarios_seguidores') ?>
                <?php endif ?>
                <button class="btn btn-primary" data-toggle="modal" data-target="#perguntas-respostas" title="Visualizar o histórico de perguntas e respostas do item">
                    <i class="glyphicon glyphicon-question-sign"></i> Perguntas e Respostas
                </button>
            </div>
            <div id="header-container-buttons-second-line">
                <?php if (has_role('admin') || has_role('consultor')) : ?>
                    <a class="btn btn-default" target="_blank" href="<?php echo site_url("cadastros/logs/detalhe?part_number=" . rawurlencode($item->part_number) . "&estabelecimento=" . rawurlencode($item->estabelecimento) . "&id_empresa={$item->id_empresa}") ?>">
                        <i class="glyphicon glyphicon-list"></i> Logs
                    </a>
                <?php endif ?>

                <a href="<?php echo site_url("homologacao") ?>" class="btn btn-default"><i class="glyphicon glyphicon-arrow-left"></i> Voltar</a>
            </div>
        </div>
    </h3>
</div>

<div class="container-tabs">
    <ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#ficha" aria-controls="ficha" role="tab" data-toggle="tab">Informações do item</a>
        </li>
        <?php if (in_array('integracao_ecomex', $funcoes_adicionais)) : ?>
            <li>
                <a href="#informacoes_comex" aria-controls="informacoes_comex" role="tab" data-toggle="tab">Informações Comex</a>
            </li>
        <?php endif; ?>
        <li>
            <a href="#pendencias" aria-controls="pendencias" role="tab" data-toggle="tab">Atribuições pendentes <span class="badge progress-bar-danger"><?php echo $total_pendencias; ?></span></a>
        </li>
    </ul>

    <div class="tab-content">
        <div role="tabpanel" class="tab-pane active" id="ficha">
            <table style="margin: 15px 0;" class="table borderless">
                <tbody>
                    <tr>
                        <th width="30%" class="text-right">Data de Criação:</th>
                        <td width="70%" >
                        <strong><?php echo date("d/m/Y H:i:s", strtotime($item->dat_criacao)) ?></strong>
                        
                        <?php if (!empty($item->data_modificacao)) : ?>
                        
                            <strong style="padding-left: 250px;">DATA DE MODIFICAÇÃO:</strong>
                        
                            <strong style="padding-left: 10px;"><?php echo date("d/m/Y H:i:s", strtotime($item->data_modificacao)) ?></strong>
                        <?php endif ?>
                        </td>
                    </tr>
                    <?php if (!empty($item->part_number)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Código do Produto:</th>
                            <td width="70%">
                                <?php if ($simplus) : ?>
                                    <?php if ($item->status_simplus == null || $item->status_simplus == 0) : ?>
                                        <i class="fa fa-circle" style="color: gray" data-toggle="tooltip" title="Item pendente de envio para a Simplus"></i>
                                    <?php elseif ($item->status_simplus == 1) : ?>
                                        <i class="fa fa-circle" style="color: green" data-toggle="tooltip" title="Item enviado para Simplus"></i>
                                    <?php endif ?>
                                <?php endif ?>

                                <?php echo $item->part_number; ?>
                            </td>
                        </tr>
                    <?php endif ?>

                    <?php if ($multi_estabelecimentos == 1) : ?>
                        <tr>
                            <th width="30%" class="text-right">Estabelecimento:</th>
                            <td width="70%"><?php echo !empty($item->estabelecimento) ? $item->estabelecimento : 'N/A'; ?></td>
                        </tr>
                    <?php endif ?>

                    <?php if (!empty($item->descricao_atual)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Descrição Atual:</th>
                            <td width="70%" class="word-break"><?php echo $item->descricao_atual ?></td>
                        </tr>
                    <?php endif ?>

                    <?php
                    $desc_proposta_resumida = $this->cad_item_model->get_descricao_resumida($item->id_item);
                    if (empty($desc_proposta_resumida)) 
                    {
                        $desc_proposta_resumida = '<em>Não informado</em>';
                    }
                    ?>
                        <tr>
                            <th width="30%" class="text-right">Descrição Proposta Resumida:</th>
                            <td width="70%" class="word-break">
                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                    <a class="input-editable proposta-resumida" data-type="text" data-pk="<?php echo $item->id_item ?>" data-max-chars="<?php echo $descricao_max_caracteres; ?>" data-url="<?php echo base_url("homologacao/ajax_atualizar_item") ?>" data-title="Informe a descrição proposta resumida.">
                                        <?php echo $desc_proposta_resumida ?>
                                    </a>
                                    <br>
                                    <small class="pull-left" id="count_proposta_resumida_ficha"></small>
                                <?php else : ?>
                                    <?php echo $desc_proposta_resumida ?>
                                <?php endif ?>
                            </td>
                        </tr>

                    <?php if ($hasDescricaoGlobal) : ?>
                        <tr>
                            <th width="30%" class="text-right">Descrição Global:</th>
                                <td class="word-break">
                                    <?php echo $item->descricao_global ?>
                                </td>
                        </tr>
                    <?php endif ?>

                    <?php if (!empty($item->status_implementacao)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Status de Implementação: </th>
                            <?php

                            switch ($item->status_implementacao) {
                                case 'I':
                                    $label_status = ' Implementado';
                                    $icon = 'glyphicon glyphicon-ok-sign text-success';
                                    break;

                                case 'N':
                                    $label_status = ' Não implementado';
                                    $icon = 'glyphicon glyphicon-minus-sign text-info';
                                    break;

                                case 'R':
                                    $label_status = ' Revisão';
                                    $icon = 'glyphicon glyphicon-remove-sign text-danger';
                                    break;
                            }
                            ?>
                            <td width="70%"><i class="<?php echo $icon; ?>"></i><?php echo $label_status; ?></td>
                        </tr>
                    <?php endif ?>

                    <?php if (in_array('descricao_proposta_completa', $campos_adicionais)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Descrição Proposta Completa:</th>
                            <td width="70%" class="d-flex align-items-center word-break" style="display: inline-block;">
                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                    <a class="input-editable" data-type="text" data-pk="<?php echo $item->id_item ?>" data-url="<?php echo base_url("homologacao/ajax_atualizar_descricao_completa") ?>" data-title="Informe a Descrição proposta completa.">
                                        <?php echo $item->descricao_proposta_completa ? $item->descricao_proposta_completa : NULL; ?>
                                    </a>

                                    <?php if (company_can("formatar_texto", $funcoes_adicionais)) : ?>
                                        <i class="glyphicon glyphicon-info-sign i-linked" data-toggle="tooltip" title="Ao salvar, o texto será formatado automaticamente. Para visualizar, atualize a página."></i>
                                    <?php endif; ?>
                                <?php else : ?>
                                    <?php echo $item->descricao_proposta_completa ? $item->descricao_proposta_completa : NULL; ?>
                                <?php endif ?>
                            </td>
                        </tr>
                    <?php endif ?>

                    <?php if (in_array('owner', $campos_adicionais)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Owner:</th>
                            <td width="70%" class="d-flex align-items-center word-break" style="display: inline-block;">
                                <?php echo !empty($owner) ? $owner : ''; ?>
                            </td>
                        </tr>
                    <?php endif ?>

                    <?php if (!empty($item->observacoes)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Observações: </th>
                            <td width="70%" class="word-break">
                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                    <a class="input-editable" data-type="text" data-pk="<?php echo $item->id_item ?>" data-url="<?php echo base_url("homologacao/ajax_atualizar_observacoes") ?>" data-title="Informe as Observações do item.">
                                        <?php echo $item->observacoes ?>
                                    </a>
                                <?php else : ?>
                                    <?php echo $item->observacoes ?>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endif; ?>

                    <?php if (in_array('funcao', $campos_adicionais)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Função: </th>
                            <td width="70%" class="d-flex align-items-center word-break">
                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                    <a class="input-editable" data-type="text" data-pk="<?php echo $item->id_item ?>" data-url="<?php echo base_url("homologacao/ajax_atualizar_funcao") ?>" data-title="Informe a Função do item.">
                                        <?php echo $item->funcao ? $item->funcao : NULL ?>
                                    </a>

                                    <?php if (company_can("formatar_texto", $funcoes_adicionais)) : ?>
                                        <i class="glyphicon glyphicon-info-sign i-linked" data-toggle="tooltip" title="Ao salvar, o texto será formatado automaticamente. Para visualizar, atualize a página."></i>
                                    <?php endif; ?>
                                <?php else : ?>
                                    <?php echo $item->funcao ? $item->funcao : NULL ?>
                                <?php endif ?>
                            </td>
                        </tr>
                    <?php endif ?>

                    <?php if (in_array('inf_adicionais', $campos_adicionais)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Informações Adicionais: </th>
                            <td width="70%" class="word-break">
                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                    <a class="input-editable" data-type="text" data-pk="<?php echo $item->id_item ?>" data-url="<?php echo base_url("homologacao/ajax_atualizar_inf_adicionais") ?>" data-title="Informe a Função do item.">
                                        <?php echo $item->inf_adicionais ? $item->inf_adicionais : NULL ?>
                                    </a>
                                <?php else : ?>
                                    <?php echo $item->inf_adicionais ? $item->inf_adicionais : NULL ?>
                                <?php endif ?>
                            </td>
                        </tr>
                    <?php endif ?>
                    <?php if (in_array('controle_drawback', $campos_adicionais)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Controle Drawback: </th>
                            <td width="70%">
                                <input type="checkbox" id="is_drawback" name="is_drawback" <?php echo $item->is_drawback == 1 ? 'checked' : '' ?> disabled value="1"> 
                            </td>
                        </tr>

                    <?php endif ?>
                    <?php if (in_array('peso', $campos_adicionais)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Peso: </th>
                            <td width="70%">
                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                    <a class="input-editable" data-type="text" data-pk="<?php echo $item->id_item ?>" data-url="<?php echo base_url("homologacao/ajax_atualizar_peso") ?>" data-title="Informe o Peso do item.">
                                        <?php echo $item->peso ? $item->peso : NULL ?>
                                    </a>
                                <?php else : ?>
                                    <?php echo $item->peso ? $item->peso : NULL ?>
                                <?php endif ?>
                            </td>
                        </tr>
                    <?php endif ?>

                    <?php if (in_array('prioridade', $campos_adicionais)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Prioridade: </th>
                            <td width="70%">
                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>

                                    <?php if (customer_has_role('alterar_criticidade',sess_user_id())) : ?>
                                        <a class="input-editable-select" data-type="select" data-source='<?php echo json_encode($prioridades)?>' data-value="<?php echo $item->id_prioridade ? $item->id_prioridade : NULL ?>" data-pk="<?php echo $item->id_item ?>" data-url="<?php echo base_url("homologacao/ajax_atualizar_prioridade") ?>" data-title="Informe a Prioridade do item.">
                                        <?php echo $item->empresa_prioridade? $item->empresa_prioridade : NULL ?>
                                        </a>
                                    <?php else : ?> 
                                        <?php echo $item->empresa_prioridade ?>
                                    <?php endif ?>
                                <?php else : ?>
                                    <?php echo $item->empresa_prioridade ? $item->empresa_prioridade : NULL ?>
                                <?php endif ?>
                            </td>
                        </tr>
                    <?php endif ?>

                    <?php if (in_array('aplicacao', $campos_adicionais)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Aplicação: </th>
                            <td width="70%" class="d-flex align-items-center">
                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                    <a class="input-editable" data-type="text" data-pk="<?php echo $item->id_item ?>" data-url="<?php echo base_url("homologacao/ajax_atualizar_aplicacao") ?>" data-title="Informe a Aplicação do item.">
                                        <?php echo $item->aplicacao ? $item->aplicacao : NULL ?>
                                    </a>

                                    <?php if (company_can("formatar_texto", $funcoes_adicionais)) : ?>
                                        <i class="glyphicon glyphicon-info-sign i-linked" data-toggle="tooltip" title="Ao salvar, o texto será formatado automaticamente. Para visualizar, atualize a página."></i>
                                    <?php endif; ?>
                                <?php else : ?>
                                    <?php echo $item->aplicacao ? $item->aplicacao : NULL ?>
                                <?php endif ?>
                            </td>
                        </tr>
                    <?php endif ?>

                    <?php if (in_array('marca', $campos_adicionais)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Marca: </th>
                            <td width="70%" class="d-flex align-items-center">
                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                    <a class="input-editable" data-type="text" data-pk="<?php echo $item->id_item ?>" data-url="<?php echo base_url("homologacao/ajax_atualizar_marca") ?>" data-title="Informe a Marca do item.">
                                        <?php echo $item->marca ? $item->marca : NULL ?>
                                    </a>

                                    <?php if (company_can("formatar_texto", $funcoes_adicionais)) : ?>
                                        <i class="glyphicon glyphicon-info-sign i-linked" data-toggle="tooltip" title="Ao salvar, o texto será formatado automaticamente. Para visualizar, atualize a página."></i>
                                    <?php endif; ?>
                                <?php else : ?>
                                    <?php echo $item->marca ? $item->marca : NULL ?>
                                <?php endif ?>
                            </td>
                        </tr>
                    <?php endif ?>

                    <?php if (in_array('material_constitutivo', $campos_adicionais)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Material Constitutivo: </th>
                            <td width="70%" class="align-items-center">
                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                    <a class="input-editable" data-type="text" data-pk="<?php echo $item->id_item ?>" data-url="<?php echo base_url("homologacao/ajax_atualizar_material_constitutivo") ?>" data-title="Informe o Material Constitutivo do item.">
                                        <?php echo $item->material_constitutivo ? $item->material_constitutivo : NULL ?>
                                    </a>

                                    <?php if (company_can("formatar_texto", $funcoes_adicionais)) : ?>
                                        <i class="glyphicon glyphicon-info-sign i-linked" data-toggle="tooltip" title="Ao salvar, o texto será formatado automaticamente. Para visualizar, atualize a página."></i>
                                    <?php endif; ?>
                                <?php else : ?>
                                    <?php echo $item->material_constitutivo ? $item->material_constitutivo : NULL ?>
                                <?php endif ?>
                            </td>
                        </tr>
                    <?php endif ?>
                    <?php if (in_array('origem', $campos_adicionais)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Origem: </th>
                            <td width="70%" class="align-items-center">
                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                    <a class="input-editable" data-type="text" data-pk="<?php echo $item->id_item ?>" data-url="<?php echo base_url("homologacao/ajax_atualizar_origem") ?>" data-title="Informe a Origem (País) do item.">
                                        <?php echo $item->origem ? $item->origem : NULL ?>
                                    </a>

                                    <?php if (company_can("formatar_texto", $funcoes_adicionais)) : ?>
                                        <i class="glyphicon glyphicon-info-sign i-linked" data-toggle="tooltip" title="Ao salvar, o texto será formatado automaticamente. Para visualizar, atualize a página."></i>
                                    <?php endif; ?>
                                <?php else : ?>
                                    <?php echo $item->origem ? $item->origem : NULL ?>
                                <?php endif ?>
                            </td>
                        </tr>
                    <?php endif ?>
                    <?php if (in_array('maquina', $campos_adicionais)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Maquina: </th>
                            <td width="70%" class="align-items-center">
                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                    <a class="input-editable" data-type="text" data-pk="<?php echo $item->id_item ?>" data-url="<?php echo base_url("homologacao/ajax_atualizar_maquina") ?>" data-title="Informe a Maquina do item.">
                                        <?php echo $item->maquina ? $item->maquina : NULL ?>
                                    </a>

                                    <?php if (company_can("formatar_texto", $funcoes_adicionais)) : ?>
                                        <i class="glyphicon glyphicon-info-sign i-linked" data-toggle="tooltip" title="Ao salvar, o texto será formatado automaticamente. Para visualizar, atualize a página."></i>
                                    <?php endif; ?>
                                <?php else : ?>
                                    <?php echo $item->maquina ? $item->maquina : NULL ?>
                                <?php endif ?>
                            </td>
                        </tr>
                    <?php endif ?>
                    <?php $has_permission = customer_has_role('atribuir_grupo_tarifario_part_number', sess_user_id()); ?>
                    <?php if (!empty($item->grupo_tarifario_desc)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Grupo Tarifário:</th>
                            <td width="70%">
                                <?php if (!empty($item->observacao)) : ?>
                                    <a class="display-obs" data-toggle="tr-observacao" href="javascript: void(0)" style="margin-right: 5px; margin-top: 5px; width: 20px;">
                                        <i class="glyphicon glyphicon-info-sign pull-left" data-toggle="tooltip" title="Critérios de Enquadramento"></i>
                                    </a>
                                <?php endif; ?>

                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                    <div id="link-grupo-tarifario" style="width: calc(100% - 30px); display:inline-block;">
                                        <a class="editable editable-click">
                                            <?php echo $item->grupo_tarifario_desc ?>
                                        </a>
                                    </div>
                                    <div class="form-group" id="autocomplete-grupo-tarifario" style="width: calc(100% - 30px); display: none;">
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="select-id_grupo_tarifario" value="<?php echo $item->grupo_tarifario_desc ?>" placeholder="" autocomplete="off" style="border-radius: 4px !important;" />
                                            <input type="hidden" name="id_grupo_tarifario" id="select-id_grupo_tarifario_hidden" value="<?php echo $item->id_grupo_tarifario ?>" />
                                            
                                            <span class="input-group-btn">
                                                <div type="button" id="button-search-grupo-tarifario">
                                                    <img src="<?php echo site_url('/assets/img/loading.gif'); ?>" width="15" height="15" style="display: none;margin-left: 10px;margin-top: 3px;float: left;">
                                                    <span style="width:15px;height:15px;display:inline-block;">&nbsp;</span>
                                                    <?php if ($has_permission) : ?>
                                                    <a href="javascript:void(0)" id="atualizar_grupo_tarifario" style="margin-left: 10px;margin-top: 3px;float: left;color: #3DB23D;display: none;" data-toggle="tooltip" data-placement="top" title="" data-pk="<?php echo $item->id_item ?>" data-original-title="Vincular Grupo Tarifário">
                                                        <i class="glyphicon glyphicon-check"></i>
                                                    </a>
                                                    <?php endif; ?>
                                                </div>
                                            </span>
                                        </div>
                                    </div>
                                <?php else : ?>

                                    <?php echo $item->grupo_tarifario_desc ?>

                                <?php endif; ?>

                                <?php if (!empty($item->observacao)) : ?>
                                    <div class="hide" id="tr-observacao" style="margin-left: 8px;">
                                        <p><?php echo $item->observacao; ?></p>
                                    </div>
                                <?php endif ?>
                            </td>
                        </tr>
                    <?php endif ?>

                    <?php if (!empty($item->ncm_atual)) : ?>
                        <tr>
                            <th width="30%" class="text-right">NCM Atual: </th>
                            <td width="70%">
                                <?php if (isset($impostos_ncm_atual)) : ?>
                                    <a class="display-ncm-table" data-toggle="ncm-atual" href="javascript: void(0)">
                                        <i class="glyphicon glyphicon-info-sign"></i> <?php echo $item->ncm_atual ?>
                                    </a>
                                    <strong>II: </strong>
                                    <?php
                                    $ii =  ($impostos_ncm_atual->pct_ii != NULL ? $impostos_ncm_atual->pct_ii . '%' : 'NT ');
                                    $tooltip = !empty($impostos_ncm_atual->pct_ii_normal) ? ('II Normal: ' . $impostos_ncm_atual->pct_ii_normal . '%') : '';
                                    echo text_tooltip_alt($ii, $tooltip);
                                    ?>

                                    <strong>IPI: </strong>
                                    <?php
                                    $ipi =  ($impostos_ncm_atual->pct_ipi != NULL ? $impostos_ncm_atual->pct_ipi . '%' : 'NT ');
                                    $tooltip = !empty($impostos_ncm_atual->pct_ipi_normal) ? ('IPI Normal: ' . $impostos_ncm_atual->pct_ipi_normal . '%') : '';
                                    echo text_tooltip_alt($ipi, $tooltip);
                                    ?>

                                    <strong>PIS: </strong> <?php echo ($impostos_ncm_atual->pct_pis != NULL ? $impostos_ncm_atual->pct_pis . '%' : 'NT '); ?>
                                    <strong>COFINS: </strong> <?php echo ($impostos_ncm_atual->pct_cofins != NULL ? $impostos_ncm_atual->pct_cofins . '%' : 'NT '); ?>
                                <?php elseif (isset($impostos_ncm_historico)) : ?>
                                    <a class="display-ncm-table text-danger" data-toggle="ncm-atual" href="javascript: void(0)">
                                        <i class="glyphicon glyphicon-warning-sign"></i> <?php echo $item->ncm_atual ?>
                                    </a>
                                    <strong>II: </strong> <?php echo ($impostos_ncm_historico->pct_ii != NULL ? $impostos_ncm_historico->pct_ii . '%' : 'NT '); ?>
                                    <strong>IPI: </strong> <?php echo ($impostos_ncm_historico->pct_ipi != NULL ? $impostos_ncm_historico->pct_ipi . '%' : 'NT '); ?>
                                    <strong>PIS: </strong> <?php echo ($impostos_ncm_historico->pct_pis != NULL ? $impostos_ncm_historico->pct_pis . '%' : 'NT '); ?>
                                    <strong>COFINS: </strong> <?php echo ($impostos_ncm_historico->pct_cofins != NULL ? $impostos_ncm_historico->pct_cofins . '%' : 'NT '); ?>
                                    <br>
                                    <small class="text-danger" style="font-size: 10px;">NCM inexistente na versão atual da TEC</small>
                                <?php else : ?>
                                    <span class="text-danger">
                                        <i class="glyphicon glyphicon-warning-sign"></i> <?php echo $item->ncm_atual ?>
                                        <strong>Impostos não encontrados</strong>
                                    </span>
                                <?php endif ?>
                            </td>
                        </tr>

                        <tr class="hide" id="ncm-atual">
                            <td colspan="2">
                                <div id="tabs-ncm-atual">
                                    <?php
                                    $this->load->view('components/detalhamento_ncm_tabs', array(
                                        'ncm' => $item->ncm_atual
                                    )); ?>
                                </div>
                            </td>
                        </tr>
                    <?php endif ?>

                    <?php if (!empty($item->ncm_fornecedor)) : ?>
                        <tr>
                            <th width="30%" class="text-right">NCM Fornecedor: </th>
                            <td width="70%">
                                <?php if (isset($impostos_ncm_fornecedor)) : ?>
                                    <a class="display-ncm-table" data-toggle="ncm-fornecedor" href="javascript: void(0)">
                                        <i class="glyphicon glyphicon-info-sign"></i> <?php echo $item->ncm_fornecedor; ?>
                                    </a>
                                    <strong>II: </strong>
                                    <?php
                                    $ii =  ($impostos_ncm_fornecedor->pct_ii != NULL ? $impostos_ncm_fornecedor->pct_ii . '%' : 'NT ');
                                    $tooltip = !empty($impostos_ncm_fornecedor->pct_ii_normal) ? ('II Normal: ' . $impostos_ncm_fornecedor->pct_ii_normal . '%') : '';
                                    echo text_tooltip_alt($ii, $tooltip);
                                    ?>

                                    <strong>IPI: </strong>
                                    <?php
                                    $ipi =  ($impostos_ncm_fornecedor->pct_ipi != NULL ? $impostos_ncm_fornecedor->pct_ipi . '%' : 'NT ');
                                    $tooltip = !empty($impostos_ncm_fornecedor->pct_ipi_normal) ? ('IPI Normal: ' . $impostos_ncm_fornecedor->pct_ipi_normal . '%') : '';
                                    echo text_tooltip_alt($ipi, $tooltip);
                                    ?>

                                    <strong>PIS: </strong> <?php echo ($impostos_ncm_fornecedor->pct_pis != NULL ? $impostos_ncm_fornecedor->pct_pis . '%' : 'NT '); ?>
                                    <strong>COFINS: </strong> <?php echo ($impostos_ncm_fornecedor->pct_cofins != NULL ? $impostos_ncm_fornecedor->pct_cofins . '%' : 'NT '); ?>
                                <?php elseif (isset($impostos_ncm_fornecedor_historico)) : ?>
                                    <a class="display-ncm-table text-danger" data-toggle="ncm-fornecedor" href="javascript: void(0)">
                                        <i class="glyphicon glyphicon-warning-sign"></i> <?php echo $item->ncm_fornecedor ?>
                                    </a>
                                    <strong>II: </strong> <?php echo ($impostos_ncm_fornecedor_historico->pct_ii != NULL ? $impostos_ncm_fornecedor_historico->pct_ii . '%' : 'NT '); ?>
                                    <strong>IPI: </strong> <?php echo ($impostos_ncm_fornecedor_historico->pct_ipi != NULL ? $impostos_ncm_fornecedor_historico->pct_ipi . '%' : 'NT '); ?>
                                    <strong>PIS: </strong> <?php echo ($impostos_ncm_fornecedor_historico->pct_pis != NULL ? $impostos_ncm_fornecedor_historico->pct_pis . '%' : 'NT '); ?>
                                    <strong>COFINS: </strong> <?php echo ($impostos_ncm_fornecedor_historico->pct_cofins != NULL ? $impostos_ncm_fornecedor_historico->pct_cofins . '%' : 'NT '); ?>
                                    <br>
                                    <small class="text-danger" style="font-size: 10px;">NCM inexistente na versão atual da TEC</small>
                                <?php else : ?>
                                    <span class="text-danger">
                                        <i class="glyphicon glyphicon-warning-sign"></i> <?php echo $item->ncm_fornecedor ?>
                                        <strong>Impostos não encontrados</strong>
                                    </span>
                                <?php endif ?>
                            </td>
                        </tr>

                        <tr class="hide" id="ncm-fornecedor">
                            <td colspan="2">
                                <div id="tabs-ncm-fornecedor">
                                    <?php
                                    $this->load->view('components/detalhamento_ncm_tabs', array(
                                        'ncm'    => $item->ncm_fornecedor,
                                        'titulo' => 'fornecedor'
                                    )); ?>
                                </div>
                            </td>
                        </tr>
                    <?php endif ?>

                    <?php if (!empty($item->ncm_proposto)) : ?>
                        <tr>
                            <th width="30%" class="text-right">NCM Proposto:</th>
                            <td width="70%">
                                <?php if (isset($impostos_ncm_proposto)) : ?>
                                    <a class="display-ncm-table" data-toggle="ncm-proposto" href="javascript: void(0)">
                                        <i class="glyphicon glyphicon-info-sign"></i> <?php echo $item->ncm_proposto ?>
                                    </a>
                                    <strong>II: </strong>
                                    <?php
                                    $ii = ($impostos_ncm_proposto->pct_ii != NULL ? $impostos_ncm_proposto->pct_ii . '%' : 'NT ');
                                    $tooltip = !empty($impostos_ncm_proposto->pct_ii_normal) ? ('II Normal: ' . $impostos_ncm_proposto->pct_ii_normal . '%') : '';
                                    echo text_tooltip_alt($ii, $tooltip);
                                    ?>

                                    <strong>IPI: </strong>
                                    <?php
                                    $ipi = ($impostos_ncm_proposto->pct_ipi != NULL ? $impostos_ncm_proposto->pct_ipi . '%' : 'NT ');
                                    $tooltip = !empty($impostos_ncm_proposto->pct_ipi_normal) ? ('IPI Normal: ' . $impostos_ncm_proposto->pct_ipi_normal . '%') : '';
                                    echo text_tooltip_alt($ipi, $tooltip);
                                    ?>

                                    <strong>PIS: </strong> <?php echo ($impostos_ncm_proposto->pct_pis != NULL ? $impostos_ncm_proposto->pct_pis . '%' : 'NT '); ?>
                                    <strong>COFINS: </strong> <?php echo ($impostos_ncm_proposto->pct_cofins != NULL ? $impostos_ncm_proposto->pct_cofins . '%' : 'NT '); ?>
                                <?php elseif (isset($impostos_ncm_proposto_hist)) : ?>
                                    <a class="display-ncm-table text-danger" data-toggle="ncm-proposto" href="javascript: void(0)">
                                        <i class="glyphicon glyphicon-warning-sign"></i> <?php echo $item->ncm_proposto ?>
                                    </a>
                                    <strong>II: </strong> <?php echo ($impostos_ncm_proposto_hist->pct_ii != NULL ? $impostos_ncm_proposto_hist->pct_ii . '%' : 'NT '); ?>
                                    <strong>IPI: </strong> <?php echo ($impostos_ncm_proposto_hist->pct_ipi != NULL ? $impostos_ncm_proposto_hist->pct_ipi . '%' : 'NT '); ?>
                                    <strong>PIS: </strong> <?php echo ($impostos_ncm_proposto_hist->pct_pis != NULL ? $impostos_ncm_proposto_hist->pct_pis . '%' : 'NT '); ?>
                                    <strong>COFINS: </strong> <?php echo ($impostos_ncm_proposto_hist->pct_cofins != NULL ? $impostos_ncm_proposto_hist->pct_cofins . '%' : 'NT '); ?>
                                    <br>
                                    <small class="text-danger" style="font-size: 10px;">NCM inexistente na versão atual da TEC</small>
                                <?php else : ?>
                                    <span class="text-danger">
                                        <i class="glyphicon glyphicon-warning-sign"></i> <?php echo $item->ncm_proposto ?>
                                        <strong>Impostos não encontrados</strong>
                                    </span>
                                <?php endif ?>
                            </td>
                        </tr>
                        <tr id="aliquotas_ex">
                            <?php if ((isset($item->num_ex_ii) && !empty($item->num_ex_ii) && $item->num_ex_ii != '-1') || (isset($item->num_ex_ipi) && !empty($item->num_ex_ipi) && $item->num_ex_ipi != '-1')) : ?>
                                <th width="30%"></th>
                                <td>
                                    <small>ALÍQUOTA DE EXCEÇÃO <?php echo isset($item_ex_ii->pct_ii) ? '<strong>II: </strong><span class="red">' . $item_ex_ii->pct_ii . '% </span>' : '' ?> <?php echo isset($item_ex_ipi->pct_ipi) ? '<strong>IPI: </strong><span class="red">' . $item_ex_ipi->pct_ipi . '% </span>' : '' ?></small>
                                </td>
                            <?php endif; ?>
                        </tr>
                        <?php if (customer_can('ii')) : ?>
                            <tr id="tr_item_num_ex_ii">
                                <?php if (isset($item->num_ex_ii) && !empty($item->num_ex_ii) && $item->num_ex_ii != '-1') : ?>
                                    <th width="30%" class="text-right">EX de II:</th>
                                    <td width="70%">
                                        <?php echo $item_ex_ii->descricao_linha1; ?>
                                    </td>
                                <?php endif; ?>
                            </tr>
                        <?php endif; ?>

                        <?php if (customer_can('ipi')) : ?>
                            <tr id="tr_item_num_ex_ipi">
                                <?php if (isset($item->num_ex_ipi) && !empty($item->num_ex_ipi) && $item->num_ex_ipi != '-1') : ?>
                                    <th width="30%" class="text-right">EX de IPI:</th>
                                    <td width="70%">
                                        EX <?php echo $item_ex_ipi->num_ex; ?> - <?php echo $item_ex_ipi->descricao_linha1; ?>
                                    </td>
                                <?php endif; ?>
                            </tr>
                        <?php endif; ?>

                        <tr class="hide" id="ncm-proposto">
                            <td colspan="2">
                                <div id="tabs-ncm-proposto">
                                    <?php $this->load->view('components/detalhamento_ncm_tabs', array(
                                        'ncm' => $item->ncm_proposto,
                                        'tab_id_fix' => 'proposto'
                                    )); ?>
                                </div>
                            </td>
                        </tr>

                        <?php if (customer_can('classificacao_energetica', false, false)) : ?>
                            <tr id="tr_item_classificacao_energetica">
                                <?php if (isset($item->id_classificacao_energetica) && !empty($item->id_classificacao_energetica) && $item->id_classificacao_energetica != '-1') : ?>
                                    <th width="30%" class="text-right">Classificação Energética:</th>
                                    <?php $classi_item = $this->ncm_model->get_classificacao_energetica_id($item->id_classificacao_energetica); ?>
                                    <td width="70%">
                                        <strong><?php echo $classi_item->ind_efic_ener ?></strong> - <span class="red"><strong>ALÍQUOTA DE IPI:</strong> <?php echo number_format($classi_item->aliquota, 1, ',', ' ');  ?>%</span>
                                    </td>
                                <?php endif; ?>
                            </tr>
                        <?php endif; ?>
                    <?php endif ?>

                    <?php ob_start() ?>

                    <?php if ($has_fotos || $has_ctr_fotos) : ?>
                        <tr <?php echo $part_number_similar && !$has_ctr_fotos ? 'class="alert-info"' : "" ?>>
                            <th width="30%" class="text-right">
                                <?php if ($part_number_similar && !$has_ctr_fotos) : ?>
                                    <div>Fotos do mesmo grupo:</div>
                                    <span data-toggle="tooltip" title="Fotos similares ao produto <?php echo $part_number_similar ?>" class="badge badge-item-similar"><?php echo $part_number_similar ?></span>
                                <?php else : ?>
                                    <div>Fotos:</div>
                                <?php endif ?>
                            </th>

                            <td width="70%">
                                <?php if ($part_number_similar && $has_fotos && !$has_ctr_fotos) : ?>
                                    <a href="javascript: //" onclick="listar_fotos(this)" class="btn-block btn btn-primary">Clique aqui para ver fotos de outros itens com mesma NCM proposta</a>
                                <?php endif ?>

                                <div id="lista-fotos" class="clearfix">
                                    <?php foreach ($fotos as $foto) : ?>
                                        <div class="col-xs-10 col-sm-4 col-md-4 col-lg-2">
                                            <a href="<?php echo base_url($foto) ?>" rel="group" class="thumbnail fancybox">
                                                <img src="<?php echo base_url($foto) ?>" ddata-src="holder.js/100x75" alt="..." style="max-height: 68px">
                                            </a>
                                        </div>
                                    <?php endforeach; ?>

                                    <?php if ($part_number_similar && $has_fotos) : ?>
                                        <div class="clearfix well" style="padding: 8px 10px; clear: both">
                                            <em>* Esta foto de caráter ilustrativo refere-se a um outro produto que recebeu a mesma classificação fiscal.</em>
                                            <?php if (!$item->status_homologacao) : ?>
                                                <br /><em>** Não é requisito obrigatório para a aprovação, que esta foto seja idêntica ao produto que se está analisando, desde que sejam respeitadas as características essenciais do grupo de itens "<?php echo $item->grupo_tarifario_desc ?>".</em>
                                            <?php endif ?>
                                        </div>
                                    <?php endif ?>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>

                    <?php
                    $listagem_fotos = ob_get_contents();
                    ob_end_clean();

                    if (!$part_number_similar || $has_ctr_fotos) :
                        echo $listagem_fotos;
                    endif;
                    ?>

                    <?php
                    if (!empty($item->cod_cest_atual) || !empty($item->cod_cest_proposto)) :
                        // Código CEST Atual
                        if (!empty($item->cod_cest_atual)) :
                            if ($item->cod_cest_atual != '-1') :
                                $cest_atual = $this->cest_model->get_entry($item->cod_cest_atual);
                                $cest_atual_str = sprintf("<b>%s</b> - %s", $item->cod_cest_atual, $cest_atual->descricao);
                            else :
                                $cest_atual_str = 'N/D - Não atende';
                            endif;

                        else :
                            $cest_atual_str = '<em>Não informado</em>';
                        endif;

                        // Código CEST Proposto
                        if (!empty($item->cod_cest_proposto)) :
                            if ($item->cod_cest_proposto != '-1') :
                                $cest_proposto = $this->cest_model->get_entry($item->cod_cest_proposto);
                                $cest_proposto_str = sprintf("<b>%s</b> - %s", $item->cod_cest_proposto, $cest_proposto->descricao);
                            else :
                                $cest_proposto_str = '<em>N/D - Não atende</em>';
                            endif;
                        else :
                            $cest_proposto_str = '<em>Não informado</em>';
                        endif;
                    ?>
                        <?php if (customer_can('cest')) : ?>
                            <tr>
                                <th width="30%" class="text-right">CEST Atual: </th>
                                <td width="70%"><?php echo $cest_atual_str ?></td>
                            </tr>

                            <tr>
                                <th width="30%" class="text-right">CEST Proposto: </th>
                                <td width="70%"><?php echo $cest_proposto_str ?></td>
                            </tr>
                        <?php endif; ?>
                    <?php endif ?>

                    <?php if (!empty($item->caracteristicas)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Características: </th>
                            <td width="70%" class="word-break">
                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                    <a class="input-editable" data-type="text" data-pk="<?php echo $item->id_item ?>" data-url="<?php echo base_url("homologacao/ajax_atualizar_caracteristica") ?>" data-title="Informe a Característica do item.">
                                        <?php echo $item->caracteristicas ?>
                                    </a>
                                <?php else : ?>
                                    <?php echo $item->caracteristicas ?>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endif ?>

                    <?php if (!empty($item->subsidio)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Subsídio: </th>
                            <td width="70%" class="word-break">
                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                    <a class="input-editable" data-type="text" data-pk="<?php echo $item->id_item ?>" data-url="<?php echo base_url("homologacao/ajax_atualizar_subsidio") ?>" data-title="Informe o Subsídio do item.">
                                        <?php echo $item->subsidio ?>
                                    </a>
                                <?php else : ?>
                                    <?php echo $item->subsidio ?>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endif ?>

                    <?php if (in_array('dispositivo_legal', $campos_adicionais)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Dispositivo legal: </th>
                            <td width="70%">
                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                    <a class="input-editable" data-type="text" data-pk="<?php echo $item->id_item ?>" data-url="<?php echo base_url("homologacao/ajax_atualizar_dispositivo_legal") ?>" data-title="Informe o Dispositivo Legal do item.">
                                        <?php echo $item->dispositivo_legal ?>
                                    </a>
                                <?php else : ?>
                                    <?php echo $item->dispositivo_legal ? $item->dispositivo_legal : '<em>Não informada</em>' ?>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endif ?>

                    <?php if (!empty($item->solucao_consulta)) : ?>
                        <tr>
                            <th width="30%" class="text-right">Solução de consulta: </th>
                            <td width="70%">
                                <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                    <a class="input-editable" data-type="text" data-pk="<?php echo $item->id_item ?>" data-url="<?php echo base_url("homologacao/ajax_atualizar_solucao_consulta") ?>" data-title="Informe a Solução de Consulta do item." >
                                        <?php echo $item->solucao_consulta ?>
                                    </a>
                                <?php else : ?>
                                    <?php echo $item->solucao_consulta ?>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endif ?>
                    <?php
                    $memoria_classificacao = $item->memoria_classificacao;
                    if (empty($memoria_classificacao)) 
                    {
                        $memoria_classificacao = '<em>Não informado</em>';
                    }
                    ?>
                    <tr>
                        <th width="30%" class="text-right">Memória de Classificação: </th>
                        <td width="70%">
                            <?php if ($perfil_allow_edit === TRUE && $allow_edit === TRUE) : ?>
                                <a style="white-space: pre-line;" class="input-editable"
                                data-type="text"
                                data-pk="<?php echo $item->id_item ?>"
                                data-url="<?php echo base_url("homologacao/ajax_atualizar_memoria_classificacao") ?>"
                                data-title="Informe a Memória de classificação do item."><?php echo trim($memoria_classificacao) ?></a>

                            <?php else : ?>
                                <?php echo trim($memoria_classificacao) ?>
                            <?php endif; ?>
                        </td>
                    </tr>

                    <?php if (count($ficha_tecnica)) : ?>
                        <tr>
                            <th width="30%" class="text-right" style="vertical-align: middle">Ficha Técnica:</th>
                            <td width="70%">
                                <?php
                                $arquivo = current($ficha_tecnica);
                                $download_url = site_url('download?arquivo=' . $arquivo->nome_hash);
                                ?>
                                <div class="input-group">
                                    <input type="text" class="form-control input-sm" readonly value="<?php printf("%s.%s", $arquivo->nome_arquivo, $arquivo->extensao) ?>">

                                    <div class="input-group-btn">
                                        <a href="<?php echo $download_url ?>" type="button" class="btn btn-default btn-sm" aria-haspopup="true" aria-expanded="false">
                                            <i class="glyphicon glyphicon-cloud-download"></i> Baixar
                                        </a>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>

                    <?php
                    if ($part_number_similar && !$has_ctr_fotos) :
                        echo $listagem_fotos;
                    endif;
                    ?>
                </tbody>
            </table>
        </div>
        <?php if (in_array('integracao_ecomex', $funcoes_adicionais)) : ?>
            <div role="tabpanel" class="tab-pane" id="informacoes_comex">
                <table style="margin: 15px 0;" class="table borderless">
                    <tbody>
                        <?php if (!empty($item->part_number)) : ?>
                            <tr>
                                <th width="30%" class="text-right">Código do Produto:</th>
                                <td width="70%">
                                    <?php if ($simplus) : ?>
                                        <?php if ($item->status_simplus == null || $item->status_simplus == 0) : ?>
                                            <i class="fa fa-circle" style="color: gray" data-toggle="tooltip" title="Item pendente de envio para a Simplus"></i>
                                        <?php elseif ($item->status_simplus == 1) : ?>
                                            <i class="fa fa-circle" style="color: green" data-toggle="tooltip" title="Item enviado para Simplus"></i>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php echo $item->part_number; ?>
                                </td>
                            </tr>
                        <?php endif; ?>

                        <?php if (!empty($item->indicador_ecomex)) : ?>
                            <tr>
                                <th width="30%" class="text-right">Indicador Comex:</th>
                                <td width="70%">
                                    <?php echo $item->indicador_ecomex; ?>
                                </td>
                            </tr>
                        <?php endif; ?>

                        <?php if (!empty($item->num_di)) : ?>
                            <tr>
                                <th width="30%" class="text-right">DI:</th>
                                <td width="70%">
                                    <?php echo $item->num_di; ?>
                                </td>
                            </tr>
                        <?php endif; ?>

                        <?php if (!empty($item->data_di)) : ?>
                            <tr>
                                <th width="30%" class="text-right">Data da Última DI:</th>
                                <td width="70%">
                                    <?php 
                                        $date = strtotime($item->data_di);
                                        $format_date = date('d/m/Y', $date);
                                    
                                        echo $format_date;
                                    ?>
                                </td>
                            </tr>
                        <?php endif; ?>

                        <?php if (!empty($item->ind_drawback) || ($item->ind_drawback == 0)) : ?>
                            <tr>
                                <th width="30%" class="text-right">Drawback (S ou N):</th>
                                <td width="70%">
                                    <?php echo ($item->ind_drawback == 1 || $item->ind_drawback == 'S') ? "S" : "N"; ?>
                                </td>
                            </tr>
                        <?php endif; ?>

                       
                        <?php if (!empty($item->data_invoice)) : ?>
                            <tr>
                                <th width="30%" class="text-right">DATA PREVISTA PO/INVOICE:</th>
                                <td width="70%">
                                    <?php 
                                        $data_invoice = strtotime($item->data_invoice);
                                        $format_date_invoice = date('d/m/Y', $data_invoice);
                                    
                                        echo $format_date_invoice;
                                    ?>                                
                                </td>
                            </tr>
                        <?php endif; ?>

                        <?php if (!empty($item->ncm_ecomex)) : ?>
                            <tr>
                                <th width="30%" class="text-right">NCM Comex:</th>
                                <td width="70%">
                                    <?php echo $item->ncm_ecomex; ?>
                                </td>
                            </tr>
                        <?php endif; ?>
                        <?php if (!empty($item->codigo_receita)) : ?>
                            <tr>
                                <th width="30%" class="text-right">Código da Receita:</th>
                                <td width="70%">
                                    <?php echo $item->codigo_receita; ?>
                                </td>
                            </tr>
                        <?php endif; ?>
                        <?php if (!empty($item->versao_produto)) : ?>
                            <tr>
                                <th width="30%" class="text-right">Versão do Produto:</th>
                                <td width="70%">
                                    <?php echo $item->versao_produto; ?>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
        <div role="tabpanel" class="tab-pane" id="pendencias" style="padding-top: 20px;">
            <div id="ncm_table_pendencias">
                <?php
                if (isset($item->ncm_proposto) && !empty($item->ncm_proposto)) {
                    $ncm_entries = $this->ncm_model->get_entries_levels($item->ncm_proposto);

                    $data['ncm_entries'] = $ncm_entries;
                    $data['ncm'] = $item->ncm_proposto;

                    $this->load->view('homologacao/ncm_table', $data);
                }
                ?>
            </div>

            <?php //if ( (( in_array("integracao_ecomex", $funcoes_adicionais) && $item->indicador_ecomex == 'EI' ) || (!in_array("integracao_ecomex", $funcoes_adicionais)))  && $has_attrs && $can_atr) : 
                if ($has_attrs && $can_atr && $item->status_atual != 4) : ?>
            
                    <h2>Atributos</h2>
                    <hr />

                    <div class="row">
                        <div class="col-xs-12">
                            <?php $this->load->view('item/attr_addedit'); ?>
                        </div>
                    </div>
                <?php endif; ?>

            <?php if (customer_can('ii') || customer_can('ipi')) : ?>
                <h2>EX Tarifário</h2>
                <hr />
                <div id="validate_ex_response"></div>
                <table style="margin: 15px 0;" class="table borderless">
                    <tbody>
                        <?php if (customer_can('ii')) : ?>
                            <tr>
                                <th width="8%" class="text-right">EX de II:</th>
                                <td class="edit_field" id="edit_field_ii">
                                    <?php
                                    $disabled = NULL;
                                    $disabled_label = 'Selecione o EX de II';
                                    if (empty($ex_tarifarios_ii)) {
                                        $disabled = 'disabled';
                                        $disabled_label = 'Nenhum EX de II disponível para a NCM';
                                    }
                                    ?>
                                    <div class="select_ex_tarifario" style="display: table; width: 96%; margin-top: -5px; float: left;" id="select_ex_ii">
                                        <select class="form-control selectpicker" data-show-icon="true" data-live-search="true" <?php echo $disabled; ?> id="ex_ii" name="ex_ii">
                                            <option disabled selected><?php echo $disabled_label; ?></option>
                                            <?php foreach ($ex_tarifarios_ii as $ex_ii) { ?>
                                                <option value="<?php echo $ex_ii->num_ex; ?>" <?php echo ($ex_ii->num_ex == $item->num_ex_ii) ? 'selected' : NULL; ?> title="<?php echo strlen($ex_ii->descricao_linha1) >= 115 ? substr($ex_ii->descricao_linha1, 0, 115) : $ex_ii->descricao_linha1; ?>" data-subtext='<i data-ex="true" class="glyphicon glyphicon-info-sign info-ex"><div style="display: none"><?php echo str_replace("'", "", str_replace('"', "", $ex_ii->descricao_linha1)) ?></div></i>'><?php echo strlen($ex_ii->descricao_linha1) >= 115 ? substr($ex_ii->descricao_linha1, 0, 115) : $ex_ii->descricao_linha1; ?></option>
                                            <?php } ?>
                                            <option value="-1" <?php echo ($item->num_ex_ii == -1) ? 'selected' : NULL; ?>>Item não atende EX</option>
                                        </select>
                                    </div>
                                    <?php if ($disabled == NULL) { ?>
                                        <a href="javascript:void(0)" class="save_changes" data-type="ex_ii" data-id="<?php echo $item->id_item; ?>" style="margin-left: 10px;margin-top: 3px;float: left;color: #3DB23D;" data-toggle="tooltip" data-placement="top" title="Vincular EX Tarifário"><i class="glyphicon glyphicon-check"></i></a>
                                    <?php } ?>
                                </td>
                            </tr>
                        <?php endif; ?>
                        <?php if (customer_can('ipi')) : ?>
                            <tr>
                                <th width="8%" class="text-right">EX de IPI:</th>
                                <td class="edit_field" id="edit_field_ipi">
                                    <?php
                                    $disabled = NULL;
                                    $disabled_label = 'Selecione o EX de IPI';
                                    if (empty($ex_tarifarios_ipi)) {
                                        $disabled = 'disabled';
                                        $disabled_label = 'Nenhum EX de IPI disponível para a NCM';
                                    }
                                    ?>
                                    <div class="select_ex_tarifario" style="display: table; width: 96%; margin-top: -5px; float: left;" id="select_ex_ipi">
                                        <select class="form-control selectpicker" <?php echo $disabled; ?> id="ex_ipi" name="ex_ipi">
                                            <option disabled selected><?php echo $disabled_label; ?></option>
                                            <?php foreach ($ex_tarifarios_ipi as $ex_ipi) { ?>
                                                <option value="<?php echo $ex_ipi->num_ex; ?>" <?php echo ($ex_ipi->num_ex == $item->num_ex_ipi) ? 'selected' : NULL; ?> title="<?php echo substr($ex_ipi->descricao_linha1, 0, 115); ?>" data-subtext='<i data-ex="true" class="glyphicon glyphicon-info-sign info-ex"><div style="display: none"><?php echo $ex_ipi->descricao_linha1 ?></div></i>'><?php echo substr($ex_ipi->descricao_linha1, 0, 115); ?></option>
                                            <?php } ?>
                                            <option value="-1" <?php echo ($item->num_ex_ipi == -1) ? 'selected' : NULL; ?>>Item não atende EX</option>
                                        </select>
                                    </div>
                                    <?php if ($disabled == NULL) { ?>
                                        <a href="javascript:void(0)" class="save_changes" data-type="ex_ipi" data-id="<?php echo $item->id_item; ?>" style="margin-left: 10px;margin-top: 3px;float: left;color: #3DB23D;" data-toggle="tooltip" data-placement="top" title="Vincular EX Tarifário"><i class="glyphicon glyphicon-check"></i></a>
                                    <?php } ?>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            <?php endif; ?>
            <h2>LI</h2>
                <hr />
                <div class="row">
                <?php if (isset($lis) && count($lis) > 0 && isset($impostos_ncm_proposto) && !empty($impostos_ncm_proposto)) { ?>
                    <div class="col-sm-12">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th>ABRANGÊNCIA</th>
                                    <th>TIPO TRATAMENTO</th>
                                    <th>ORGÃO ANUENTE</th>
                                    <th>FINALIDADE</th>
                                    <th>DESTAQUE</th>
                                    <th>DESCRIÇÃO</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $ultimo_li = null;
                                $show_999 = false;
                                if (!empty($list)) {
                                    foreach ($lis as $li) { ?>
                                        <?php if ($li->TIPO_TRATAMENTO == 'DESTAQUE DE MERCADORIA') $show_999 = true; ?>
                                        <tr>
                                            <td>
                                                <input type="radio" name="li" class="li-checked" <?php echo ($item->li_destaque == $li->DESTAQUE) ? 'checked' : ''; ?>  data-orgao-anuente="<?php echo $li->ORGAO_ANUENTE ?>" data-destaque="<?php echo $li->DESTAQUE ?>" data-pk="<?php echo $item->id_item ?>">
                                            </td>
                                            <td><?php echo $li->ABRANGENCIA ?></td>
                                            <td><?php echo $li->TIPO_TRATAMENTO ?></td>
                                            <td><?php echo $li->ORGAO_ANUENTE ?></td>
                                            <td><?php echo $li->FINALIDADE ?></td>
                                            <td><?php echo $li->DESTAQUE ?></td>
                                            <td><?php echo $li->DESCRICAO ?></td>
                                        </tr>
                                    <?php $ultimo_li = $li;
                                    }
                                } ?>
                                <?php if ($show_999) : ?>
                                    <tr>
                                        <td>
                                            <input type="radio" name="li" class="li-checked" <?php echo ($item->li_destaque == '999') ? 'checked' : ''; ?>  data-orgao-anuente="<?php echo $ultimo_li->ORGAO_ANUENTE ?>" data-destaque="999" data-pk="<?php echo $item->id_item ?>">
                                        </td>
                                        <td><?php echo $ultimo_li->ABRANGENCIA ?></td>
                                        <td><?php echo $ultimo_li->TIPO_TRATAMENTO ?></td>
                                        <td></td>
                                        <td><?php echo $ultimo_li->FINALIDADE ?></td>
                                        <td>999</td>
                                        <td></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                        <div class="pull-right">
                            <button type="button" class="btn btn-primary" id="btn-atualizar-li" data-pk="<?php echo $item->id_item ?>">
                                Atualizar LI
                            </button>
                        </div>
                    </div>
                    <?php
                } else {
                    if (isset($impostos_ncm_proposto) && !empty($impostos_ncm_proposto)) {
                    ?>
                        <div class="col-sm-12">
                            <p>Não há atributos LI disponíveis para o NCM informado [<strong><?php echo $item->ncm_proposto ?></strong>].</p>
                        </div>
                    <?php } else { ?>
                        <div class="col-sm-12">
                            <p>Informe um NCM para escolher o atributo.</p>
                        </div>
                <?php
                    }
                }
                ?>
            </div>

            <h2>Antidumping</h2>
                <hr />
                <div class="row">
                    <?php if (isset($antidumpings) && count($antidumpings) > 0 && isset($item->ncm_proposto) && !empty($item->ncm_proposto)) { ?>
                        <div class="col-sm-12">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th>PAÍS</th>
                                        <th>MEDIDA</th>
                                        <th>DESCRIÇÃO</th>
                                        <th>DATA VIGÊNCIA FIM</th>
                                        <th>DATA INCLUSÃO REG.</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                        if (!empty($antidumpings)) {
                                            foreach ($antidumpings as $antidumping) {
                                    ?>
                                                <tr>
                                                    <td>
                                                        <input type="radio" name="antidumping" class="antidumping-checked" <?php echo ($item->antidumping == 'SIM') ? 'checked' : ''; ?> data-antidumping="<?php echo $antidumping->MEDIDA ?>" data-pk="<?php echo $item->id_item ?>">
                                                    </td>
                                                    <td><?php echo $antidumping->PAIS ?></td>
                                                    <td><?php echo $antidumping->MEDIDA ?></td>
                                                    <td><?php echo $antidumping->DESCRICAO ?></td>
                                                    <td><?php echo $antidumping->DAT_VIGENCIA_FIM ?></td>
                                                    <td><?php echo $antidumping->DAT_INCLUSAO_REG ?></td>
                                                </tr>
                                    <?php
                                            }
                                        }
                                    ?>
                                </tbody>
                            </table>

                            <div class="pull-right">
                                <button type="button" class="btn btn-primary" id="btn-att-antidumping" data-pk="<?php echo $item->id_item ?>">
                                    Atualizar Antidumping
                                </button>
                            </div>

                        </div>
                        <?php
                    } else {
                        if (isset($item->ncm_proposto) && !empty($item->ncm_proposto)) {
                        ?>
                            <div class="col-sm-12">
                                <p>Não há atributos ANTIDUMPING disponíveis para o NCM informado [<strong><?php echo $item->ncm_proposto ?></strong>].</p>
                            </div>
                        <?php } else { ?>
                            <div class="col-sm-12">
                                <p>Informe um NCM para escolher o atributo.</p>
                            </div>
                    <?php
                        }
                    }
                    ?>
                </div>
              
            <?php if (customer_can('nve')) : ?>
                <h2>Nomenclatura de Valor Aduaneiro e Estatística - NVE</h2>
                <hr />
                <div id="atributos-nve" class="form-horizontal" style="margin: 20px 0">
                    <?php

                    $data = array();

                    $data['id_grupo_tarifario'] = $item->id_grupo_tarifario;
                    $data['ncm'] = $item->ncm_proposto;
                    $data['atributos'] = $atributos_ncm;
                    $data['id_item'] = $item->id_item;

                    $this->load->view('homologacao/subview_nve', $data);

                    ?>
                </div>
            <?php endif; ?>
            <?php if (customer_can('cest')) : ?>
                <h2>CEST</h2>
                <hr />
                <div id="validate_cest_response"></div>
                <table style="margin: 15px 0;" class="table borderless">
                    <tbody>
                        <tr>
                            <th width="8%" class="text-right">
                                CEST
                            </th>
                            <td class="edit_field" id="edit_cest">
                                <?php
                                $disabled = NULL;
                                $disabled_label = 'Selecione o CEST';
                                if (empty($cests)) {
                                    $disabled = 'disabled';
                                    $disabled_label = 'Nenhum CEST disponível para a NCM';
                                }
                                ?>
                                <div class="select_cest_tarifario" style="display: table; width: 96%; margin-top: -5px; float: left;" id="select_cest">
                                    <select class="form-control selectpicker" <?php echo $disabled; ?> id="cest" name="cest">
                                        <option disabled selected><?php echo $disabled_label; ?></option>
                                        <?php foreach ($cests as $cest) { ?>
                                            <option value="<?php echo $cest->cod_cest; ?>" <?php echo ($cest->cod_cest == $item->cod_cest_proposto) ? 'selected' : NULL; ?> title="<?php echo substr($cest->descricao, 0, 110); ?>" data-subtext='<i class="glyphicon glyphicon-info-sign info-ex"><div style="display: none"><?php echo $cest->descricao ?></div></i>'>
                                                <?php echo $cest->cod_cest . ' - ' . substr($cest->descricao, 0, 100); ?>

                                            </option>
                                        <?php } ?>
                                        <option value="-1" <?php echo ($item->cod_cest_proposto == -1) ? 'selected' : NULL; ?>>
                                            Item não atende CEST
                                        </option>
                                    </select>
                                </div>
                                <?php if ($disabled == NULL) { ?>
                                    <a href="javascript:void(0)" class="save_changes_cest" data-type="cest" data-id="<?php echo $item->id_item; ?>" style="margin-left: 10px;margin-top: 3px;float: left;color: #3DB23D;" data-toggle="tooltip" data-placement="top" title="Vincular CEST"><i class="glyphicon glyphicon-check"></i></a>
                                <?php } ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            <?php endif; ?>

            <?php if (customer_can('classificacao_energetica', false, false)) : ?>
                <div class="row">
                    <div class="col-sm-6">
                        <h2 style="margin-bottom:0px;">Classificação Energética</h2>
                    </div>
                </div>

                <div class="row">
                    <div id="validate_classificacao_energetica_response"></div>
                    <table style="margin: 15px 0;" class="table borderless">
                        <tbody>
                            <tr>
                                <th width="8%" class="text-right">
                                    Classificação Energética
                                </th>
                                <td class="edit_field" id="edit_classificacao_energetica">
                                    <div class="select_classificacao_nergetica" style="display: table; width: 96%; margin-top: -5px; float: left;" id="select_classificacao_energetica">
                                        <select class="form-control selectpicker" id="classificacao_energetica" name="classificacao_energetica">
                                            <?php if ($item->id_classificacao_energetica == NULL) : ?>
                                                <option <?php echo $item->id_classificacao_energetica == NULL ? 'selected' : ''; ?> disabled>Não Atribuido</option>
                                            <?php endif; ?>
                                            <option value="-1" <?php echo $item->id_classificacao_energetica == '-1' ? 'selected' : ''; ?>>Não atende</option>
                                            <?php foreach ($classificacoes_energeticas as $classificacao_energetica) { ?>
                                                <option value="<?php echo $classificacao_energetica->id; ?>" <?php echo $classificacao_energetica->id == $item->id_classificacao_energetica ? 'selected' : NULL; ?> title="<?php echo "{$classificacao_energetica->ncm} - {$classificacao_energetica->ind_efic_ener} - {$classificacao_energetica->aliquota}%"; ?>">
                                                    <?php echo "<strong>{$classificacao_energetica->ncm}</strong> - {$classificacao_energetica->ind_efic_ener} - {$classificacao_energetica->aliquota}%"; ?>
                                                </option>
                                            <?php } ?>
                                        </select>
                                    </div>
                                    <?php if (!empty($classificacoes_energeticas)) : ?>
                                        <a href="javascript:void(0)" class="save_change_classificacao_energetica <?php echo $item->id_classificacao_energetica == NULL ? 'hide' : ''; ?>" data-type="classificacao_energetica" data-id="<?php echo $item->id_item; ?>" style="margin-left: 10px;margin-top: 3px;float: left;color: #3DB23D;" data-toggle="tooltip" data-placement="top" title="Vincular Classificação Energética">
                                            <i class="glyphicon glyphicon-check"></i>
                                        </a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            <?php endif ?>

            <?php if (customer_can('suframa')) : ?>
                <div class="row">
                    <div class="col-sm-6">
                        <h2 style="margin-bottom:0px;">SUFRAMA</h2>
                    </div>
                    <div class="col-sm-6 text-right">
                        <button style="margin-top:20px;" class="btn" type="button" data-toggle="modal" data-target="#modal-suframa">Gerenciar SUFRAMA</button>
                    </div>
                </div>
                <hr />
                <div class="form-group">
                    <div class="input-group">
                        <table style="margin: 15px 0;" class="table borderless">
                            <tbody>
                                <tr>
                                    <th width="8%" class="text-right">CÓDIGO:</th>
                                    <td><?php echo !empty($item->suframa_codigo) ? $item->suframa_codigo : '-' ?></td>
                                </tr>
                                <tr>
                                    <th width="8%" class="text-right">PRODUTO:</th>
                                    <td><?php echo !empty($item->suframa_produto) ? $item->suframa_produto : '-' ?></td>
                                </tr>
                                <tr>
                                    <th width="8%" class="text-right">DESTAQUE:</th>
                                    <td><?php echo !empty($item->suframa_destaque) ? $item->suframa_destaque : '-' ?></td>
                                </tr>
                                <tr>
                                    <th width="8%" class="text-right">DESCRIÇÃO:</th>
                                    <td><?php echo !empty($item->suframa_descricao) ? $item->suframa_descricao : '-' ?></td>
                                </tr>
                                <tr>
                                    <th width="8%" class="text-right">PPB:</th>
                                    <td><?php echo !empty($item->suframa_ppb) ? $item->suframa_ppb : '-' ?></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

            <?php endif ?>

            <?php if (company_can('lessin')) : ?>
                <div class="row">
                    <div class="col-sm-12">
                        <h2>LESSIN <button class="btn btn-primary" title="Será atualizado os valores da lista Becomex com base na NCM Proposta" id="save-lessin">Atualizar <i class="glyphicon glyphicon-info-sign"></i></button></h2>
                    </div>
                    <div class="col-sm-12">
                        <div id="validate-lessin">

                        </div>
                    </div>
                </div>

                <hr />

                <div class="form-group">
                    <div class="input-group" id="lessin-load-html">
                        <?php $this->load->view('homologacao/ficha-lessin', array('lessin' => $itemLessin, 'item' => $item)); ?>
                    </div>
                </div>

                <hr />
            <?php endif ?>

            <div class="clearfix"></div>
        </div>
    </div>
    <?php
    if (!$simplus && $item->id_status <> 5) {
        $check_item = $check_item_list = NULL;
        $title = "";

        if (has_role('engenheiro') && company_can('homologacao_engenharia')) {
            $check_item_list = $this->cad_item_model->check_item_homologado_by_tipo($item->id_item, 'Engenharia', FALSE, TRUE);
            $title = "Homologação Responsável Técnico";
        } else if (has_role('fiscal') && company_can('homologacao_fiscal')) {
            $check_item_list = $this->cad_item_model->check_item_homologado_by_tipo($item->id_item, 'Fiscal', FALSE, TRUE);
            $title = "Homologação Fiscal";
        }

        $users_allowed = array();

        if (!empty($check_item_list)) {
            foreach ($check_item_list as $i) {
                if ($i->id_usuario == sess_user_id()) {
                    $check_item = $i;
                }
                // $users_allowed[] = $item->id_usuario;
            }
        }

        if (!$check_item || (is_object($check_item) && isset($check_item->id_usuario) && $check_item->id_usuario == sess_user_id())) {
    ?>
            <h2><?php echo $title ?></h2>
            <hr />

            <?php echo form_open('', array('class' => 'form-horizontal', 'id' => 'form-homologacao-ficha')) ?>

            <?php

            if (isset($check_item->id_item)) {
            ?>
                <div class="form-group">
                    <label class="col-sm-2 control-label">Homologado em: </label>
                    <div class="col-sm-10">
                        <p class="form-control-static"><?php echo date("d/m/Y H:i:s", strtotime($check_item->criado_em)) ?></p>
                    </div>
                </div>
            <?php
            }

            ?>

            <?php
            $texto_obsoleto = "Informado pelo(a) Sr(a). " . sess_user_nome() . " que o item está Inativo.";
            ?>
            <div class="form-group">
                <div class="col-sm-offset-2 col-sm-10">
                    <div class="radio-inline text-success">
                        <label class="item_opt">
                            <input type="radio" name="homologado" value="1" <?php echo set_radio('homologado', 1, isset($check_item->id_item) ? ($check_item->homologado == 1) : "") ?> id="homologado1" value="option1" checked>
                            <i class="glyphicon glyphicon-thumbs-up"></i> Homologado
                        </label>
                    </div>
                    <div class="radio-inline text-danger">
                        <label class="item_opt">
                            <input type="radio" name="homologado" value="0" <?php echo set_radio('homologado', 0, isset($check_item->id_item) ? ($check_item->homologado == 0) : "") ?> id="homologado0" value="option2">
                            <i class="glyphicon glyphicon-thumbs-down"></i> Não Homologado
                        </label>
                    </div>
                    <div class="radio-inline">
                        <label class="item_opt">
                            <input type="radio" name="homologado" value="2" <?php echo set_radio('homologado', 2, isset($check_item->id_item) ? ($check_item->homologado == 2) : "") ?> id="homologado2" value="option3">
                            <i class="glyphicon glyphicon-asterisk"></i> Item Inativo
                        </label>
                    </div>
                </div>
            </div>

            <div class="row motivo-block">
                <div class="col-sm-2">
                    <label for="motivo" class="pull-right control-label">Motivo: </label>
                </div>

                <div class="col-sm-10">
                    <div class="row">
                        <div class="col-sm-12 motivo-select-block hide">
                            <select class="selectpicker form-control motivo-select" name="id_motivo_inativo">
                                <?php foreach ($motivos as $motivo) { ?>
                                    <option value="<?php echo $motivo->id_motivo ?>">
                                        <?php echo $motivo->motivo ?>
                                    </option>
                                <?php } ?>
                                <option value="">Outra situação</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-12 motivo-text-block" style="margin-top: 5px">
                            <textarea class="form-control" id="motivo" name="motivo" placeholder="Motivo" style="height: 100px"><?php echo set_value('motivo', isset($check_item->id_item) ? $check_item->motivo : "") ?></textarea>
                        </div>
                    </div>
                </div>
                <?php if (
                        customer_has_role('homologar_atributos_workflow', sess_user_id()) &&
                        $item->importado
                        ) : ?>
                    <div class="col-sm-2"></div> <!-- Alinhamento dos checkboxes -->
                    <div class="col-sm-10">
                        <div class="checkbox" style="margin-right: 2px; margin-top:5px;">
                            <label for="ckbx_wf">
                                <input id="ckbx_wf"  name="ckbx_wf" type="checkbox" value="1" />
                                <strong>Homologar também atributos</strong>
                            </label>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <div class="row" style="margin-top: 5px">
                <div class="col-sm-12">
                    <div class="row">
                        <div class="col-sm-2 pull-right">
                            <button type="submit" name="submit" value="1" class="btn btn-primary btn-block">Enviar</button>
                        </div>
                    </div>
                </div>
            </div>
            </form>
    <?php }
    }
    ?>

    <?php if (!empty($historico)) { ?>
        <br />

        <h2 class="page-header">Histórico de homologação</h2>
        <div class="col-md-12">
            <div class="row">
                <table class="table table-striped table-hover">
                    <thead>
                        <th style="width: 15%;">Tipo</th>
                        <th style="width: 20%;">Homologação</th>
                        <th style="width: 30%;">Responsável</th>
                        <th>Motivo</th>
                    </thead>
                    <tbody>
                        <?php foreach ($historico as $homologacao) {  ?>
                            <?php if (
                                (($homologacao->tipo_homologacao == 'Engenharia' && isset($homologacoes['homologacao_engenharia']) && $homologacoes['homologacao_engenharia'] === TRUE) ||
                                    ($homologacao->tipo_homologacao == 'Fiscal' && isset($homologacoes['homologacao_fiscal']) && $homologacoes['homologacao_fiscal'] === TRUE))

                            ) : ?>
                                <tr>
                                    <td><?php echo ($homologacao->tipo_homologacao == 'Engenharia') ? 'Responsável Técnico' : $homologacao->tipo_homologacao ?></td>
                                    <?php
                                    switch ($homologacao->homologado) {
                                        case 0:
                                            $status = '<span class="text-danger"><i class="glyphicon glyphicon-thumbs-down"></i> Não homologado</span>';
                                            break;
                                        case 1:
                                            $status = '<span class="text-success"><i class="glyphicon glyphicon-thumbs-up"></i> Homologado</span>';
                                            break;
                                        case 2:
                                            $status = '<span><i class="glyphicon glyphicon-asterisk"></i> Item Inativo</span>';
                                            break;
                                        default:
                                            $status = ' - ';
                                    }
                                    ?>
                                    <td><?php echo $status; ?></td>
                                    <td><?php echo $homologacao->responsavel; ?><br>
                                        <small class="text-muted"><?php echo date('d/m/Y', strtotime($homologacao->criado_em)); ?> <i class="glyphicon glyphicon-time"></i> <?php echo date('H:i:s', strtotime($homologacao->criado_em)); ?></small>
                                    </td>
                                    <td><?php echo (!empty($homologacao->motivo) ? $homologacao->motivo : " <i>Não informado</i> "); ?></td>
                                </tr>
                            <?php endif; ?>
                        <?php } ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php } ?>

    <div class="modal fade" id="perguntas-respostas">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title" id="myModalLabel">Perguntas e Respostas</h4>
                </div>
                <div class="modal-body" style="margin: 15px;">
                    <div id="app-listagem-perguntas-respostas">
                        <v-listagem-perguntas-respostas partnumber="<?php echo !empty($item->part_number) ? $item->part_number : '' ?>" estabelecimento="<?php echo !empty($item->estabelecimento) ? $item->estabelecimento : '' ?>" base-url="<?php echo base_url(); ?>"></v-listagem-perguntas-respostas>
                    </div>

                    <script type="text/javascript" src="<?php echo base_url('assets/vuejs/dist/listagem-perguntas-respostas.js?version=' . config_item('assets_version')) ?>"></script>
                    <link rel="stylesheet" href="<?php echo base_url('assets/vuejs/dist/listagem-perguntas-respostas.css?version=' . config_item('assets_version')) ?>" type="text/css" media="screen" />
                </div>
            </div>
        </div>
    </div>


    <?php if ($simplus) : ?>
        <div class="modal fade" id="simplus-modal" tabindex="-1" role="dialog" aria-labelledby="Simplus" aria-hidden="true"></div>
    <?php else : ?>
        <div class="modal fade" id="transferencia-modal" tabindex="-1" role="dialog" aria-labelledby="Transferência" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <?php echo form_open('homologacao/transferir_responsavel', array('class' => 'form-horizontal')) ?>
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h4 class="modal-title" id="myModalLabel">Transferência de responsável</h4>
                    </div>
                    <div class="modal-body" style="margin: 15px;">
                        <input type="hidden" name="id_item" value="<?php echo $item->id_item; ?>" />
                        <input type="hidden" name="url_redirect" value="homologacao/ficha/<?php echo $item->id_item; ?>" />

                        <div class="form-group">
                            <div id="message_user_transferencia_responsavel"></div>
                            <label for="tipo_responsavel">Tipo de responsável:</label>
                            <select class="form-control selectpicker" name="tipo_responsavel" id="tipo_responsavel" data-show-subtext="true" data-title="Selecione o tipo de responsável">
                                <option value="">Selecione</option>
                                <option value="id_resp_engenharia">Engenheiro</option>
                                <option value="id_resp_fiscal">Fiscal</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Usuário responsável:</label>
                            <select class="form-control selectpicker" name="id_usuario" id="id_usuario" data-live-search="true" data-show-subtext="true" data-title="Selecione o novo responsável">
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Motivo:</label>
                            <textarea class="form-control" name="motivo_transf" id="motivo_transf" rows="3" required placeholder="Motivo"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Fechar</button>
                        <button type="submit" class="btn btn-primary" id="button-transfer-submit" name="transferir-commit" value="1">Salvar</button>
                    </div>
                    <?php echo form_close(); ?>
                </div>
            </div>
        </div>
    <?php endif ?>

    <div class="modal fade" id="modal-descricao" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="modal-title"></h4>
                </div>
                <div id="modal-conteudo" class="modal-body">
                </div>
            </div>
        </div>
    </div>

    <?php if (customer_can('suframa')) : ?>
        <input type="hidden" id="suframa-produto-ncm" value="<?php echo $item->ncm_proposto; ?>">
        <?php $this->load->view('homologacao/ficha-modal-suframa'); ?>
    <?php endif; ?>

    <style>
        #aliquotas_ex small,
        small span.red,
        span.red {
            color: red;
        }

        #aliquotas_ex small strong {
            color: #333;
        }

        .suframa-select {
            max-width: 800px
        }

        .modal-dialog.modal-lg {
            width: 75% !important;
        }

        #modal-suframa-conteudo .bootstrap-select .dropdown-menu {
            max-width: 100%;
        }

        .i-linked {
            color: rgb(51 122 183);
            margin-left: 5px;
        }

        .d-flex {
            display: flex;
        }

        .align-items-center {
            align-items: center;
        }

        .word-break {
            word-break: break-word;
        }
    </style>

    <script type="text/javascript" src="<?php echo base_url('assets/js/fancybox/jquery.mousewheel-3.0.6.pack.js') ?>"></script>

    <link rel="stylesheet" href="<?php echo base_url('assets/js/fancybox/jquery.fancybox.css?v=2.1.5') ?>" type="text/css" media="screen" />
    <script type="text/javascript" src="<?php echo base_url('assets/js/fancybox/jquery.fancybox.pack.js?v=2.1.5') ?>"></script>

    <link rel="stylesheet" href="<?php echo base_url('assets/js/fancybox/helpers/jquery.fancybox-buttons.css?v=1.0.5') ?>" type="text/css" media="screen" />
    <script type="text/javascript" src="<?php echo base_url('assets/js/fancybox/helpers/jquery.fancybox-buttons.js?v=1.0.5') ?>"></script>
    <script type="text/javascript" src="<?php echo base_url('assets/js/fancybox/helpers/jquery.fancybox-media.js?v=1.0.6') ?>"></script>

    <link rel="stylesheet" href="<?php echo base_url('assets/js/fancybox/helpers/jquery.fancybox-thumbs.css?v=1.0.7') ?>" type="text/css" media="screen" />
    <script type="text/javascript" src="<?php echo base_url('assets/js/fancybox/helpers/jquery.fancybox-thumbs.js?v=1.0.7') ?>"></script>

    <link rel="stylesheet" href="<?php echo base_url('assets/css/bootstrap-editable.css') ?>" type="text/css" media="screen" />
    <script type="text/javascript" src="<?php echo base_url('assets/js/bootstrap-editable.min.js') ?>"></script>


    <script type="text/javascript">
        $(document).ready(function() {
            $('#classificacao_energetica').on('change', function() {
                if ($(this).val()) {
                    $('.save_change_classificacao_energetica').removeClass('hide');
                }
            });

            let currentSelection = null;
            let previousSelection = null;

            // Inicializa currentSelection com o radio button já selecionado
            function initializeSelection() {
                const checkedRadio = $('.li-checked:checked');
                if (checkedRadio.length > 0) {
                    currentSelection = {
                        element: checkedRadio,
                        li: "SIM",
                        li_orgao_anuente: checkedRadio.data('orgao-anuente'),
                        li_destaque: checkedRadio.data('destaque'),
                        pk: checkedRadio.data('pk')
                    };
                }
            }

            // Chama a função de inicialização
            initializeSelection();

            // Quando um radio button é clicado
            $('.li-checked').on('change', function() {
                previousSelection = currentSelection;
                currentSelection = {
                    element: $(this),
                    li: "SIM",
                    li_orgao_anuente: $(this).data('orgao-anuente'),
                    li_destaque: $(this).data('destaque'),
                    pk: $(this).data('pk')
                };
            });

            // Quando o botão "Atualizar LI" é clicado
            $('#btn-atualizar-li').on('click', function() {
                if (currentSelection) {
                    $.ajax({
                        url: '<?php echo site_url("homologacao/ajax_atualizar_li"); ?>',
                        data: {
                            pk: currentSelection.pk,
                            li: currentSelection.li,
                            li_orgao_anuente: currentSelection.li_orgao_anuente,
                            li_destaque: currentSelection.li_destaque,
                            previous_li_orgao_anuente: previousSelection ? previousSelection.li_orgao_anuente : '',
                            previous_li_destaque: previousSelection ? previousSelection.li_destaque : ''
                        },
                        method: 'POST',
                        success: function(resp) {
                            if (resp.status == 200) {
                                swal({
                                    title: "Sucesso!",
                                    text: "O LI foi alterado.",
                                    type: "success"
                                }).then(function() {
                                    // Opcional: recarregar a página ou atualizar a interface
                                    // window.location.reload();
                                });
                            }
                        },
                        error: function(err) {
                            swal({
                                title: "Atenção!",
                                text: 'Ocorreu um erro ao alterar o LI',
                                type: "error",
                            });
                        }
                    });
                } else {
                    swal({
                        title: "Atenção!",
                        text: 'Por favor, selecione uma opção antes de atualizar.',
                        type: "warning",
                    });
                }
            });

            // Quando o botão "Atualizar Antidumping" é clicado
            $('#btn-att-antidumping').on('click', function() {
                antidumping = $(this).data('antidumping');
                    pk = $(this).data('pk');
                    if (pk == undefined || pk == null || !pk) {
                        swal({
                            title: "Atenção!",
                            text: 'Não encontramos o item informado.',
                            type: "warning",
                        });
                    }

                $.ajax({
                    url: '<?php echo site_url("homologacao/ajax_atualizar_antidumping"); ?>',
                    data: {
                        pk: pk,
                        value: 'SIM',
                        descricao: antidumping
                    },
                    method: 'POST',
                    success: function(resp) {
                        if (resp.status == 200) {
                            swal({
                                title: "Sucesso!",
                                text: "O Antidumping foi alterado.",
                                type: "success"
                            }).then(function() {
                                // Opcional: recarregar a página ou atualizar a interface
                                // window.location.reload();
                            });
                        }
                    },
                    error: function(err) {
                        swal({
                            title: "Atenção!",
                            text: 'Ocorreu um erro ao alterar o Antidumping',
                            type: "error",
                        });
                    }
                });
            });

            function updateCharacterCount() {
                let count = $('.proposta-resumida').text().trim().length;
                $('#count_proposta_resumida_ficha').text('(' + count + ' caracteres' + ')');
            }

            updateCharacterCount();

            $('.input-editable').on('save', function(e, params) {
                $(this).text(params.newValue);
                updateCharacterCount();
            });
        
        });

        $(function() {
            var getSuframaProdutoUrl = '<?php echo base_url("atribuir_grupo/xhr_get_suframa_produtos_by_code") ?>';
            var suframaProdutoNcm = $('#suframa-produto-ncm');
            var suframaProdutoSelect = $('#suframa-produto-select');
            var destaqueTbody = $('.suframa-destaque-tbody');


            suframaProdutoSelect.on('change', function() {
                var codigo = $(this).val();
                var ncm = suframaProdutoNcm.val();

                if (codigo && ncm) {
                    getSuframaProduto(codigo, ncm);
                } else {
                    showTableWarningMessage();
                }
            });

            function showTableWarningMessage() {
                var message = `<tr>
        <td colspan="999">Selecione um grupo tarifário para associar</td>
    </tr>`;

                $('.suframa-destaque-tbody').html(message);
            }

            function getSuframaProduto(code, ncm) {
                $.ajax({
                    url: getSuframaProdutoUrl,
                    type: 'get',
                    dataType: "json",
                    data: {
                        'code': code,
                        'ncm': ncm
                    },
                    success: function(response) {
                        if (response.data) {
                            populateDestaque(response.data, ncm);
                        }
                    },
                    error: function() {}
                })
            }

            function populateDestaque(itens, ncm) {
                destaqueTbody.html('');
                var elements = '';

                if (!itens || itens.length == 0) {
                    elements = `<tr>
            <td colspan="999">Nenhum destaque foi encontrado para a ncm ${ncm}</td>
        </tr>`;
                } else {

                    $.each(itens, function(index, item) {

                        elements += `<tr>
                <td>
                    <input type="radio" name="destaque"
                        data-destaque="${item.DESTAQUE}"
                        data-ppb="${item.PPB}"
                        data-codigo="${item.CODIGO}"
                        data-descricao="${item.DESCRICAO_SUFRAMA}"
                        data-produto="${item.PRODUTO}"
                        value="${item.DESTAQUE}"
                    />
                </td>
                <td>${item.DESTAQUE}</td>
                <td class="suframa-ppb">${item.PPB}</td>
                <td class="suframa-codigo">${item.CODIGO}</td>
                <td class="suframa-descricao">${item.DESCRICAO_SUFRAMA}</td>
                <td class="suframa-produto hide">${item.PRODUTO}</td>
            </tr>`;

                    })
                }


                destaqueTbody.append(elements);
            }

            $('.selectpicker').selectpicker();

            $('#modal-descricao').on("click", function(e) {
                e.stopPropagation();
                e.preventDefault();
            });

            $(".info-ex").on("click", function(e) {
                e.stopPropagation();
                e.preventDefault();

                if ($(this).data('ex')) {
                    var teste = "Descrição do EX Tarifário";
                } else {
                    var teste = "Descrição do CEST";
                }

                let descricao = $(this).children().text();
                let content = '<p>' + descricao + '</p>';

                $('#modal-descricao').find('#modal-conteudo').html('');
                $('#modal-descricao').find('#modal-conteudo').html(content);
                $('#modal-descricao').find('#modal-title').html(teste);


                $("#modal-descricao").modal('show');

            });

            $('.btn-modal-simplus').on('click', function(e) {
                e.preventDefault();

                $('#simplus-modal').modal('show');

                $.ajax({
                    url: $(this).attr('href'),
                    method: 'post',
                    data: {
                        'item[]': '<?php echo $item->id_item ?>'
                    },
                    success: function(ret) {
                        $('#simplus-modal').html(ret);
                    }
                });
            });

            $('#tabs-ncm-atual a').click(function(e) {
                e.preventDefault()
                $(this).tab('show')
            });

            $('#tabs-ncm-proposto a').click(function(e) {
                e.preventDefault()
                $(this).tab('show')
            });

            $('.item_opt > input[type="radio"]').on('change', function() {
                // Inativo
                if ($(this).val() == 2) {
                    $('.motivo-block').find('.motivo-select-block').removeClass('hide');
                    $('.motivo-block').find('.motivo-text-block').addClass('hide');
                } else {
                    $('.motivo-block').find('.motivo-select-block').addClass('hide');
                    $('.motivo-block').find('.motivo-text-block').removeClass('hide');
                }
            });
            

            $('.motivo-block').find('.motivo-select').on('change', function() {
                if ($(this).val() == '') {
                    $('.motivo-block').find('.motivo-text-block').removeClass('hide');
                } else {
                    $('.motivo-block').find('.motivo-text-block').addClass('hide');
                }
            });

            $('a.fancybox').fancybox({
                'changeFade': 0,
                'padding': 0,
                'loop': false,
                'type': 'image',
                'helpers': {
                    overlay: {
                        locked: false
                    }
                }
            });

            <?php if ($perfil_allow_edit === TRUE) { ?>
                var STATUS_SIMPLUS = <?php echo $item->status_simplus ? $item->status_simplus : '0' ?>;

                $('.input-editable').editable({
                    tpl: "<input type='text' >",
                    emptytext: 'Não informado',
                    validate: function(value) {
                        if (value === null || value === '') {
                            return 'É necessário preencher o campo.';
                        }

                        if (max_chars = $(this).attr('data-max-chars')) {
                            if (max_chars != 0 && value.length > max_chars) {
                                return 'A descrição excede o limite de ' + max_chars + ' caracteres.';
                            }
                        }
                    },
                    success: function(data, newvalue) {
                        var input = $(this).data('editable');
                        var pk = input.options.pk;

                        if (STATUS_SIMPLUS) {
                            swal({
                                title: 'Integração Simplus',
                                confirmButtonText: 'Sim',
                                cancelButtonText: 'Não',
                                showCancelButton: true,
                                text: '<p><strong>Atenção!</strong> O item já foi enviado para a Simplus.</p>' +
                                    '<p>Você gostaria de voltar o status do item para <strong>pendente de envio</strong>?</p>',
                                showLoaderOnConfirm: true,
                                allowOutsideClick: false
                            }).then(function() {
                                return new Promise(function(resolve) {
                                    $.post('<?php echo site_url("homologacao/atualiza_simplus") ?>', {
                                            "id_item": pk
                                        },
                                        function(data) {
                                            swal({
                                                type: 'success',
                                                text: data.message
                                            }).then(function() {
                                                location.reload();
                                            });
                                        });
                                })
                            });
                        }
                    }
                });
                $('.input-editable-select').editable({


                    tpl: "<select>",
                    emptytext: 'Não informado',
                    validate: function(value) {
                        if (value === null || value === '') {
                            return 'É necessário preencher o campo.';
                        }
                    },
                    success: function(data, newvalue) {
                        var input = $(this).data('editable');
                        var pk = input.options.pk;

                        if (STATUS_SIMPLUS) {
                            swal({
                                title: 'Integração Simplus',
                                confirmButtonText: 'Sim',
                                cancelButtonText: 'Não',
                                showCancelButton: true,
                                text: '<p><strong>Atenção!</strong> O item já foi enviado para a Simplus.</p>' +
                                    '<p>Você gostaria de voltar o status do item para <strong>pendente de envio</strong>?</p>',
                                showLoaderOnConfirm: true,
                                allowOutsideClick: false
                            }).then(function() {
                                return new Promise(function(resolve) {
                                    $.post('<?php echo site_url("homologacao/atualiza_simplus") ?>', {
                                            "id_item": pk
                                        },
                                        function(data) {
                                            swal({
                                                type: 'success',
                                                text: data.message
                                            }).then(function() {
                                                location.reload();
                                            });
                                        });
                                })
                            });
                        }
                    }
                });
            <?php } ?>

            $('#select-id_grupo_tarifario').autocomplete({
                serviceUrl: '<?php echo base_url('homologacao/xhr_get_lista_grupos_tarif') ?>',
                minChars: 3,
                onSearchStart: function() {
                    $("#button-search-grupo-tarifario").find('span').css('display', 'none');
                    $("#button-search-grupo-tarifario").find('img').css('display', 'inline-block');
                },
                onSearchComplete: function() {
                    $("#button-search-grupo-tarifario").find('span').css('display', 'inline-block');
                    $("#button-search-grupo-tarifario").find('img').css('display', 'none');
                },
                onSelect: function(suggestion) {
                    $('#select-id_grupo_tarifario_hidden').val(suggestion.data);
                    // $('#select-id_grupo_tarifario_hidden').trigger('change');
                    $("#button-search-grupo-tarifario").find('span').css('display', 'none');
                    $('#atualizar_grupo_tarifario').css('display', 'inline-block');

                }
            });

            $('#link-grupo-tarifario').on('click', function() {
                $(this).css('display', 'none');
                $('#autocomplete-grupo-tarifario').css('display', 'inline-block');
                $('#select-id_grupo_tarifario').select();
                var width = $('#select-id_grupo_tarifario').css('width');
                $('.autocomplete-suggestions').css('min-width', width);
            })

            $('#atualizar_grupo_tarifario').on('click', function() {
                var self = $(this);
                var pk = self.attr('data-pk');
                if (pk == undefined || pk == null || !pk) {
                    swal({
                        title: "Atenção!",
                        text: 'Não encontramos o item informado.',
                        type: "warning",
                    });
                }

                var value = $('#select-id_grupo_tarifario_hidden').val();
                if (value == undefined || value == null || !value) {
                    swal({
                        title: "Atenção!",
                        text: 'É necessário que seja selecionado um Grupo Tarifário após a pesquisa.',
                        type: "warning",
                    });
                }
                $.ajax({
                    url: '<?php echo site_url("homologacao/ajax_atualizar_grupo_tarifario"); ?>',
                    data: {
                        pk: pk,
                        value: value,
                        descricao: '',
                    },
                    method: 'POST',
                    success: function(resp) {
                        if (resp.status == 200) {
                            swal({
                                title: "Sucesso!",
                                text: "O Grupo Tarifário foi alterado.",
                                type: "success"
                            }).then(function() {
                                window.location.reload();
                            })
                        }
                    },
                    error: function(err) {
                        swal({
                            title: "Atenção!",
                            text: 'Ocorreu um erro ao relacionar o Grupo Tarifário ao Item.',
                            type: "error",
                        });
                    }
                })
            });
        });

        $(function() {
            function setAlert(field, event) {
                event.preventDefault();

                $("#message_user_transferencia_responsavel").addClass("alert alert-danger");
                $("#message_user_transferencia_responsavel").html(`<div>Oops! ${field} para concluir a transferência.</div>`);
            }

            $("#button-transfer-submit").on("click", function(e) {
                if (!$("#motivo_transf").val()) {
                    setAlert("Digite um motivo", e);
                }

                if (!$("#id_usuario").val()) {
                    setAlert("Selecione o novo usuário responsável", e);
                }

                if (!$("#tipo_responsavel").val()) {
                    setAlert("Selecione o tipo do novo usuário responsável", e);
                }

                if ($("#tipo_responsavel").val() && $("#id_usuario").val()) {
                    $("#message_user_transfer").find("div").remove();
                }
            });

            $("#tipo_responsavel").on("change", function() {
                if ($(this).val()) {
                    $.post("<?php echo base_url() ?>homologacao/xhr_get_responsaveis", {
                        "tipo_responsavel": $(this).val()
                    }).done(function(data) {
                        // console.log(data);
                        populateSelect(data.select_body);
                    });
                } else {
                    populateSelect("");
                }
            });

            $("#transferencia-modal").on("hide.bs.modal", () => resetFormData());

            function resetFormData() {
                populateSelect("");

                $("#tipo_responsavel").selectpicker("val", "");
                $("#tipo_responsavel").selectpicker("refresh");
            }

            function populateSelect(content) {
                $("#id_usuario").html(content);
                $("#id_usuario").selectpicker("refresh");
            }
        });
    </script>

    <script>
        $(document).ready(function() {
            // Initialize Select2
            $('#owner-select').select2();

            // Handle selection change
            $('#owner-select').on('change', function() {
                var newOwner = $(this).val();
                var itemId = '<?php echo $item->id_item ?>';
                var updateUrl = '<?php echo base_url("homologacao/ajax_atualizar_owner") ?>';

                // Update the owner via AJAX
                $.post(updateUrl, {
                        pk: itemId,
                        value: newOwner
                    })
                    .done(function(data) {
                        console.log('Owner updated successfully.');
                    })
                    .fail(function() {
                        console.log('Error updating owner.');
                    });
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $('#revisarItem').on('click', function() {

                if ('<?php echo $item->status_slug ?>' != 'homologado_em_revisao') {
                    swal({
                        title: 'Atenção!',
                        text: 'Não é possível marcar o item como Revisado. Verifique se o status do item é "Homologado em Revisão".',
                        type: 'warning',
                    });
                    return;
                }

                let id_item = '<?php echo $item->id_item ?>';
                let part_number = '<?php echo $item->part_number ?>';
                let estabelecimento = '<?php echo $item->estabelecimento ?>';
                let id_empresa = '<?php echo $item->id_empresa ?>';
                let descricao = '<?php echo $item->descricao_atual ?>';
                let url = '<?php echo site_url("homologacao/ajax_revisar_item"); ?>';
                let urlReverter = '<?php echo site_url("homologacao/ajax_reverter_item"); ?>';

                swal({
                    title: 'Atenção!',
                    text: 'Deseja marcar o item como revisado?',
                    type: 'warning',
                    showCancelButton: true,
                    showConfirmButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#8b8f93',
                    cancelButtonText: 'Cancelar',
                    confirmButtonText: 'Sim, revisar!',
                    reverseButtons: true,
                    // html: `Deseja marcar o item como revisado?
                    // <br> <br>
                    // <br> <br> 
                    // <button type="button" value="cancel" class="btn btn-default">Cancelar</button> 
                    // <button type="button" value="revert" class="btn btn-danger" >Reverter Alterações</button>
                    // <button type="button" value="confirm" class="btn btn-success">Sim, confirmar!</button>`, 
                }).then(function () {
                    // console.log(result, url);
                        // Exibir o loading antes de iniciar a requisição AJAX
                    swal({
                        title: 'Aguarde...',
                        text: 'Atualizando o item...',
                        onOpen: () => {
                            swal.showLoading();
                        },
                        allowOutsideClick: () => !swal.isLoading()
                    });

                    $.post(url, {
                        id_item: id_item,
                        part_number: part_number,
                        estabelecimento: estabelecimento,
                        descricao: descricao,
                        id_empresa: id_empresa,
                    }).done(function(data) {
                        console.log(data);
                        if (data.success == true) {
                            swal({
                                title: "Sucesso!",
                                text: "O item foi revisado.",
                                type: "success"
                            }).then(function() {
                                window.location.reload();
                            })
                        }
                        else {
                            swal({
                                title: "Atenção!",
                                text: 'Ocorreu um erro ao revisar o item.',
                                type: "error",
                            });
                        }
                    }).fail(function() {
                        swal({
                            title: "Atenção!",
                            text: 'Ocorreu um erro ao revisar o item.',
                            type: "error",
                        });
                    });
                });

                // swal({
                //     title: 'Atenção!',
                //     text: 'Deseja marcar o item como revisado?',
                //     type: 'warning',
                //     showCancelButton: false,
                //     showConfirmButton: false,
                //     html: `Deseja marcar o item como revisado?
                //     <br> <br>
                //     <br> <br> 
                //     <button type="button" value="cancel" class="btn btn-default">Cancelar</button> 
                //     <button type="button" value="revert" class="btn btn-danger" >Reverter Alterações</button>
                //     <button type="button" value="confirm" class="btn btn-success">Sim, confirmar!</button>`,
                //     onOpen: () => {
                //         const cancelButton = document.querySelector('button[value="cancel"]');
                //         const revertButton = document.querySelector('button[value="revert"]');
                //         const confirmButton = document.querySelector('button[value="confirm"]');

                //         cancelButton.addEventListener('click', () => {
                //             swal.close();
                //         });

                //         revertButton.addEventListener('click', () => {
                //             // Exibir o loading antes de iniciar a requisição AJAX
                //             swal.showLoading();

                //             $.post(urlReverter, {
                //                 id_item: id_item,
                //                 part_number: part_number,
                //                 estabelecimento: estabelecimento,
                //                 descricao: descricao,
                //                 id_empresa: id_empresa,
                //             }).done(function(data) {
                //                 if (data.success == true) {
                //                     swal({
                //                         title: "Sucesso!",
                //                         text: "Alterações revertidas com sucesso.",
                //                         type: "success"
                //                     }).then(function() {
                //                         window.location.reload();
                //                     })
                //                 } else {
                //                     swal({
                //                         title: "Atenção!",
                //                         text: 'Ocorreu um erro ao reverter as alterações.',
                //                         type: "error",
                //                     });
                //                 }
                //             }).fail(function() {
                //                 swal({
                //                     title: "Atenção!",
                //                     text: 'Ocorreu um erro ao reverter as alterações.',
                //                     type: "error",
                //                 });
                //             });
                //         });

                //         confirmButton.addEventListener('click', () => {
                //             // Exibir o loading antes de iniciar a requisição AJAX
                //             swal.showLoading();

                //             $.post(url, {
                //                 id_item: id_item,
                //                 part_number: part_number,
                //                 estabelecimento: estabelecimento,
                //                 descricao: descricao,
                //                 id_empresa: id_empresa,
                //             }).done(function(data) {
                //                 if (data.success == true) {
                //                     swal({
                //                         title: "Sucesso!",
                //                         text: "O item foi revisado.",
                //                         type: "success"
                //                     }).then(function() {
                //                         window.location.reload();
                //                     })
                //                 } else {
                //                     swal({
                //                         title: "Atenção!",
                //                         text: 'Ocorreu um erro ao revisar o item.',
                //                         type: "error",
                //                     });
                //                 }
                //             }).fail(function() {
                //                 swal({
                //                     title: "Atenção!",
                //                     text: 'Ocorreu um erro ao revisar o item.',
                //                     type: "error",
                //                 });
                //             });
                //         });
                //     },
                // });

            });

            $("#form-homologacao-ficha").on('submit', function(e) {
                e.preventDefault();
                
                // Adicionar classe de loading ao botão
                let button = $(this).find('button[type="submit"]');
                $(button).button('loading');

                let formData = $(this).serialize();

                let id_item = '<?php echo $item->id_item ?>';
                let isFicha = 1;

                formData += `&id_item=${id_item}&isFicha=${isFicha}`;

                console.log(formData);
                
                $.ajax({
                    url: '<?php echo site_url("homologacao/ajax_homologar") ?>',
                    method: 'POST',
                    data: formData,
                    success: function(response) {
                        console.log(response, 'success');
                        if (response.status === 'warning' && response.atributos_vazios) {
                            if (response.pode_homologar) {
                                // Usuário tem permissão para homologar mesmo com atributos vazios
                                swal({
                                    title: 'Atenção!',
                                    text: 'Existem atributos obrigatórios que ainda não foram preenchidos. Deseja continuar mesmo assim?',
                                    type: 'warning',
                                    confirmButtonText: "Sim",
                                    cancelButtonText: "Não",
                                    showConfirmButton: true,
                                    showCancelButton: true
                                }).then(function(result) {
                                    if (result) {
                                        // Usuário escolheu continuar
                                        $.ajax({
                                            url: '<?php echo site_url("homologacao/ajax_homologar") ?>',
                                            method: 'POST',
                                            data: formData + '&forcar_homologacao=1',
                                            success: function(resp) {
                                                if (resp.status === 'success') {
                                                    swal({
                                                        title: 'Sucesso!',
                                                        text: resp.message,
                                                        type: 'success'
                                                    }).then(function() {
                                                        window.location.href = resp.redirect_url;
                                                    });
                                                }
                                            }
                                        });
                                    } else {
                                        window.location.href = response.redirect_url;
                                    }
                                }).catch(function() {
                                    window.location.href = response.redirect_url;
                                })
                            } else {
                                // Usuário não tem permissão para homologar com atributos vazios
                                swal({
                                    title: 'Atenção!',
                                    text: 'Existem atributos obrigatórios que ainda não foram preenchidos. Por causa disso, os atributos não foram homologados. Verifique os itens.',
                                    type: 'warning'
                                }).then(function() {
                                    window.location.href = response.redirect_url;
                                });
                            }
                        } else if (response.status === 'success') {
                            swal({
                                title: 'Sucesso!',
                                text: response.message,
                                type: 'success'
                            }).then(function() {
                                window.location.href = response.redirect_url;
                            });
                        } else {
                            console.log(response, 'error');
                            swal({
                                title: 'Erro!',
                                text: response.message,
                                type: 'error'
                            }).then(function() {
                                $(button).button('reset');
                                window.location.href = response.redirect_url;
                            })
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Erro na requisição:', status, error);
                        $(button).button('reset');
                        window.location.reload();
                    }
                });
            });

            $('#pendenteHomologacao').on('click', function() {
                if ('<?php echo $item->status_slug ?>' == 'homologar') {
                    swal({
                        title: 'Atenção!',
                        text: 'Não é possível marcar o item como Pendente de Homologação. Verifique se o status do item é "Pendente de Homologação".',
                        type: 'warning',
                    });
                    return;
                }

                let id_item = '<?php echo $item->id_item ?>';
                let part_number = '<?php echo $item->part_number ?>';
                let estabelecimento = '<?php echo $item->estabelecimento ?>';
                let id_empresa = '<?php echo $item->id_empresa ?>';
                let descricao = '<?php echo $item->descricao_atual ?>';
                let url = '<?php echo site_url("homologacao/ajax_pendente_homologacao"); ?>';

                swal({
                    title: 'Atenção!',
                    text: 'Deseja marcar o item como Pendente de Homologação?',
                    type: 'warning',
                    showCancelButton: true,
                    showConfirmButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#8b8f93',
                    cancelButtonText: 'Cancelar',
                    confirmButtonText: 'Sim, enviar!',
                    reverseButtons: true,
                }).then(function () {
                    // Exibir o loading antes de iniciar a requisição AJAX
                    swal({
                        title: 'Aguarde...',
                        text: 'Atualizando o item...',
                        onOpen: () => {
                            swal.showLoading();
                        },
                        allowOutsideClick: () => !swal.isLoading()
                    });

                    $.post(url, {
                        id_item: id_item,
                        part_number: part_number,
                        estabelecimento: estabelecimento,
                        descricao: descricao,
                        id_empresa: id_empresa,
                    }).done(function(data) {
                        console.log(data);
                        if (data.success == true) {
                            swal({
                                title: "Sucesso!",
                                text: "O item foi marcado como Pendente de homologação.",
                                type: "success"
                            }).then(function() {
                                window.location.reload();
                            })
                        } else {
                            swal({
                                title: "Atenção!",
                                text: 'Ocorreu um erro ao marcar o item como Pendente de homologação.',
                                type: "error",
                            });
                        }
                    }).fail(function() {
                        swal({
                            title: "Atenção!",
                            text: 'Ocorreu um erro ao marcar o item como Pendente de homologação.',
                            type: "error",
                        });
                    });
                });
            });

        });
        
    </script>